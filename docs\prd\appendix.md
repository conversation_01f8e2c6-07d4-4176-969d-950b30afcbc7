# Appendix

## A. Sample CSV Format

### Required Columns
The system expects the following columns in the uploaded CSV file:

| Column Name | Description | Example |
| :--- | :--- | :--- |
| id | Unique identifier for the incident | INC-2024-001234 |
| ImpactDate | Date when the incident was opened | 2024-08-15 14:30:00 |
| Service | Services affected by the incident | Email Service, User Authentication |
| ProblemService | Team responsible for the incident | Infrastructure Team |
| Summary | Brief description of the incident | Email service outage affecting 50% of users |
| BusinessImpact | Impact on business operations | High - Customer communication disrupted |
| Instructions | Current status and user guidance | Service restored. Users should clear cache. |
| TechnicalDetails | Technical information about the incident | Database connection pool exhausted due to memory leak |

### Sample CSV Content
```csv
id, ImpactDate, Service, ProblemService, Summary, BusinessImpact, Instructions, TechnicalDetails
INC-2024-001234,2024-08-15 14:30:00,"Email Service, User Authentication",Infrastructure Team,Email service outage affecting 50% of users,High - Customer communication disrupted,Service restored. Users should clear cache.,Database connection pool exhausted due to memory leak
INC-2024-001235,2024-08-15 16:45:00,Web Portal,Development Team,Login page not loading for some users,Medium - Partial user access issues,Investigating root cause. Workaround available.,Frontend build deployment failed due to missing dependencies
```

## B. LLM Evaluation Criteria

### Scoring Rubric
The LLM will evaluate each incident ticket based on the following criteria:

1. **Completeness (25%)**
   - All required fields are populated
   - Information is comprehensive and detailed
   - No critical information is missing

2. **Clarity (25%)**
   - Information is easy to understand
   - Technical details are appropriate for the audience
   - Language is clear and unambiguous

3. **Accuracy (25%)**
   - Information appears factually correct
   - Technical details are consistent
   - Timeline and impact assessments are realistic

4. **Actionability (25%)**
   - Provides clear next steps or resolution status
   - Includes relevant workarounds or user instructions
   - Enables effective incident response

### Score Scale
- **Excellent (9-10)**: Meets all criteria with exceptional quality
- **Good (7-8)**: Meets most criteria with minor gaps
- **Fair (5-6)**: Meets some criteria but has notable deficiencies
- **Poor (3-4)**: Meets few criteria with significant issues
- **Inadequate (1-2)**: Fails to meet most criteria

## C. Technical Specifications

### API Endpoints
- `POST /api/upload` - Handle CSV file upload and validation
- `POST /api/evaluate` - Process individual ticket evaluation
- `GET /api/status/{sessionId}` - Check processing status
- `POST /api/export` - Generate and download results CSV

### Environment Variables
```env
LLM_API_URL=https://your-llm-api.company.com
LLM_API_KEY=your-api-key-here
MAX_FILE_SIZE=10485760  # 10MB
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30000  # 30 seconds
```

### Dependencies
```json
{
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "papaparse": "^5.4.1",
    "tailwindcss": "^3.3.0",
    "recharts": "^2.8.0"
  }
}
```