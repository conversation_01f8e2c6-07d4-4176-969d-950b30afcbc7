'use client';

import React, { useState, useRef, useCallback } from 'react';
import { UploadResponse, FileValidationResult } from '@/types';
import { validateFile } from '@/lib/validation';

interface FileUploadProps {
  onUploadSuccess: (response: UploadResponse) => void;
  onUploadError: (error: string) => void;
  onUploadStart?: () => void;
}

// Utility functions
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function isCSVFile(file: File): boolean {
  return file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv');
}

export default function FileUpload({ onUploadSuccess, onUploadError, onUploadStart }: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadFileName, setUploadFileName] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [validationResult, setValidationResult] = useState<FileValidationResult | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle file selection from input or drag
  const handleFileSelection = useCallback(async (file: File) => {
    setSelectedFile(file);
    
    // Validate file immediately
    const validation = validateFile(file);
    
    // Additional CSV-specific validation
    if (!isCSVFile(file)) {
      const invalidValidation = {
        isValid: false,
        errors: [...validation.errors, 'Please select a CSV file'],
        fileSize: file.size,
        mimeType: file.type
      };
      setValidationResult(invalidValidation);
      return;
    }
    
    setValidationResult(validation);
    
    // Auto-proceed with upload if validation passes
    if (validation.isValid) {
      // Notify parent that upload is starting
      onUploadStart?.();
      
      // Inline upload logic to avoid circular dependency
      setIsUploading(true);
      setUploadProgress(0);
      setUploadFileName(file.name);

      try {
        // Create form data for API request
        const formData = new FormData();
        formData.append('file', file);

        // Simulate processing progress (slower for LLM evaluation)
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev >= 85) {
              clearInterval(progressInterval);
              return 85;
            }
            return prev + 5;
          });
        }, 1000);

        // Make API request
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        clearInterval(progressInterval);
        setUploadProgress(100);

        const result: UploadResponse = await response.json();

        if (!response.ok || !result.success) {
          throw new Error(result.error || 'Upload failed');
        }
        
        setIsUploading(false);
        setUploadProgress(100);
        
        onUploadSuccess(result);
        // Reset form
        setSelectedFile(null);
        setValidationResult(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } catch (error) {
        setIsUploading(false);
        setUploadProgress(0);
        onUploadError(error instanceof Error ? error.message : 'Upload failed');
      }
    }
  }, [onUploadSuccess, onUploadError, onUploadStart]);

  // Handle file drop
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelection(e.dataTransfer.files[0]);
    }
  }, [handleFileSelection]);

  // Handle file input change
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelection(e.target.files[0]);
    }
  }, [handleFileSelection]);

  // Handle file upload
  const handleUpload = useCallback(async (fileToUpload?: File) => {
    const file = fileToUpload || selectedFile;
    if (!file) {
      return;
    }

    // Notify parent that upload is starting
    onUploadStart?.();
    
    setIsUploading(true);
    setUploadProgress(0);
    setUploadFileName(file.name);

    try {
      // Create form data for API request
      const formData = new FormData();
      formData.append('file', file);

      // Simulate processing progress (slower for LLM evaluation)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 85) {
            clearInterval(progressInterval);
            return 85;
          }
          return prev + 5;
        });
      }, 1000);

      // Make API request
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      const result: UploadResponse = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Upload failed');
      }
      
      setIsUploading(false);
      setUploadProgress(100);
      
      onUploadSuccess(result);
      // Reset form
      setSelectedFile(null);
      setValidationResult(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);
      onUploadError(error instanceof Error ? error.message : 'Upload failed');
    }
  }, [selectedFile, onUploadSuccess, onUploadError, onUploadStart]);

  // Open file dialog
  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300
          ${dragActive ? 'border-indigo-400 bg-indigo-50 scale-105' : 'border-gray-300 hover:border-indigo-300 hover:bg-gray-50'}
          ${isUploading ? 'pointer-events-none opacity-50' : ''}
          bg-white shadow-sm
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {/* Upload Icon */}
        <div className="mb-6">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-indigo-100 rounded-full mb-4">
            <svg
              className="w-10 h-10 text-indigo-600"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
              aria-hidden="true"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {/* Upload Text */}
        <div className="mb-8">
          <h3 className="text-2xl font-semibold text-gray-900 mb-3">
            {dragActive ? 'Drop your CSV file here' : 'Upload Incident Data'}
          </h3>
          <p className="text-lg text-gray-600 mb-2">
            Drag and drop your incident tickets CSV file here, or click to browse
          </p>
          <p className="text-sm text-gray-500">
            Supports CSV files up to 10MB with incident ticket data
          </p>
        </div>

        {/* File Selection Button */}
        <button
          type="button"
          onClick={openFileDialog}
          disabled={isUploading}
          className="inline-flex items-center px-8 py-4 border border-transparent text-base font-medium rounded-xl shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          Select CSV File
        </button>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv,text/csv"
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>

      {/* File Information */}
      {selectedFile && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <svg className="h-5 w-5 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium text-gray-900">{selectedFile.name}</span>
            </div>
            <span className="text-sm text-gray-500">{formatFileSize(selectedFile.size)}</span>
          </div>

          {/* Validation Results - Only show errors */}
          {validationResult && !validationResult.isValid && (
            <div className="mt-2">
              <div className="text-red-600">
                <div className="flex items-center mb-1">
                  <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm font-medium">Validation Errors:</span>
                </div>
                <ul className="text-sm list-disc list-inside ml-5">
                  {validationResult.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Upload Progress */}
      {isUploading && (
        <div className="mt-4">
          <div className="bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 mt-1 text-center">
            Uploading {uploadFileName}...
          </p>
        </div>
      )}
    </div>
  );
}