'use client';

import React, { useState } from 'react';
import FileUpload from '@/components/FileUpload';
import { ResultsTable } from '@/components/ResultsTable';
import { SummaryStatistics } from '@/components/SummaryStatistics';
import { UploadResponse, EvaluatedTicket } from '@/types';

export default function HomePage() {
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<EvaluatedTicket[] | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const handleUploadSuccess = (response: UploadResponse) => {
    setResults(response.evaluatedTickets || []);
    setFileName(response.fileName || 'Unknown file');
    setError(null);
    setIsLoading(false);
  };

  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage);
    setResults(null);
    setIsLoading(false);
  };

  const handleUploadStart = () => {
    setIsLoading(true);
    setError(null);
    setResults(null);
  };

  const resetUpload = () => {
    setError(null);
    setResults(null);
    setFileName('');
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Page Header - Only show during upload screen */}
          {!results && (
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-14 h-14 bg-muted rounded-lg mb-6">
                <svg className="w-7 h-7 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h1 className="text-3xl font-semibold text-foreground mb-4">
                Flash QA
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                AI-powered incident ticket analysis and quality assessment
              </p>
            </div>
          )}



        {/* Upload Component */}
        {!results && !error && (
          <FileUpload 
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            onUploadStart={handleUploadStart}
          />
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">Processing your file with AI evaluation...</p>
            <p className="mt-2 text-sm text-gray-500">This may take a few moments depending on file size</p>
          </div>
        )}

        {/* Results Display */}
        {results && results.length > 0 && (
          <div className="space-y-8">
            {/* Header with Upload New File Button */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900">Quality Assessment Dashboard</h2>
                <p className="text-gray-600 mt-1">
                  File: <span className="font-medium">{fileName}</span> • {results.length} incidents analyzed
                </p>
              </div>
              <button
                onClick={resetUpload}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
              >
                Upload New File
              </button>
            </div>

            {/* Summary Statistics */}
            <SummaryStatistics results={results} />

            {/* Detailed Results Table */}
            <ResultsTable results={results} />
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <svg className="h-6 w-6 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <h2 className="text-lg font-semibold text-red-900">
                Upload Failed
              </h2>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-red-200 mb-4">
              <p className="text-sm text-red-700">{error}</p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={resetUpload}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        </div>
      </div>
    </div>
  );
}