// Simple test to verify LLM service works
const { createLLMService } = require('./src/lib/llmService.ts');

async function testLLM() {
  try {
    console.log('Testing LLM service...');
    
    const llmService = createLLMService();
    console.log('LLM service created successfully');
    
    const testTicket = {
      id: 'TEST-001',
      ImpactDate: '2024-01-15',
      Service: 'Network Infrastructure',
      ProblemService: 'DNS Service',
      Summary: 'DNS resolution failing for internal domains',
      BusinessImpact: 'High - Users cannot access internal applications',
      Instructions: 'Restart DNS service and check configuration',
      TechnicalDetails: 'DNS server showing high CPU usage and memory leaks'
    };
    
    const result = await llmService.evaluateTicket(testTicket);
    console.log('LLM evaluation result:', result);
    
  } catch (error) {
    console.error('Error testing LLM service:', error);
  }
}

testLLM();
