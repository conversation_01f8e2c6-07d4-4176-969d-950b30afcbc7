# Requirements

## Functional Requirements
1.  **FR1**: The system shall provide an interface for a user to upload a CSV file.
2.  **FR2**: The system shall parse a CSV file and specifically process the following columns: `id`, `ImpactDate`, `Service`, `ProblemService`, `Summary`, `BusinessImpact`, `Instructions`, and `TechnicalDetails`.
3.  **FR3**: For each row in the uploaded CSV, the system shall make an API call to the in-house, OpenAI-compatible LLM to evaluate the ticket's quality.
4.  **FR4**: The system shall display the results in a paginated on-screen table, including all original data plus the LLM-generated `Ranking` and `Review Comments` columns.
5.  **FR5**: The on-screen results table shall include a function to search by `incident id` and `team`, and a function to filter by `score`.
6.  **FR6**: The system shall generate and display summary statistics (Total processed, successful, failed) for the current upload.
7.  **FR7**: The system shall generate and display a chart showing the distribution of scores, which can be filtered by `team` (with multi-select).
8.  **FR8**: The system shall provide a function for the user to download the complete results (including new `Ranking` and `Review Comments` columns) as a new CSV file.

## Non-Functional Requirements
1.  **NFR1**: The application must be built using Next.js for both its frontend and backend components.
2.  **NFR2**: The MVP architecture must be stateless; no data from user uploads will be stored in a persistent database.
3.  **NFR3**: The implementation must prioritize simplicity and focus on core functionality, avoiding premature optimization or complex abstractions.
4.  **NFR4**: The user interface must provide clear, real-time feedback during processing, including progress status and counts of pending, in-progress, and completed records.
5.  **NFR5**: The prompt engineering for the LLM must ensure that feedback on the `business impact`, `latest update`, and `user instruction` fields is in plain English for a non-technical audience, while the `technical details` field remains precise for an IT audience.
6.  **NFR6**: The system's CSV parsing logic must be flexible, gracefully ignoring any additional columns not on the required list without causing an error.