# Deployment

## Deployment Overview

The Incident Ticket Quality Evaluator is designed for flexible deployment across multiple platforms, with primary support for Vercel and Netlify. The application follows a stateless architecture that simplifies deployment and scaling.

## Deployment Targets

### Primary: Vercel (Recommended)

**Advantages:**
- Native Next.js support with zero configuration
- Automatic API route handling
- Built-in performance optimization
- Global CDN with edge functions
- Seamless environment variable management
- Automatic HTTPS and custom domains

**Configuration:**

```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    },
    {
      "src": "/(.*)",
      "dest": "/$1"
    }
  ],
  "env": {
    "LLM_API_URL": "@llm-api-url",
    "LLM_API_KEY": "@llm-api-key",
    "NODE_ENV": "production"
  },
  "functions": {
    "app/api/**/*.js": {
      "maxDuration": 60
    }
  }
}
```

### Secondary: Netlify

**Advantages:**
- Excellent static site hosting
- Serverless function support
- Built-in form handling
- Branch-based deployments

**Configuration:**

```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = "out"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"
```

### Alternative: Docker Container

**Use Cases:**
- On-premises deployment
- Custom cloud infrastructure
- Kubernetes environments

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Environment variables must be present at build time
ARG LLM_API_URL
ARG LLM_API_KEY
ENV LLM_API_URL=${LLM_API_URL}
ENV LLM_API_KEY=${LLM_API_KEY}
ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

## Environment Configuration

### Required Environment Variables

```bash
# .env.production
LLM_API_URL=https://your-llm-api.company.com
LLM_API_KEY=your-secure-api-key
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Optional Configuration
LLM_TIMEOUT=30000
LLM_MAX_RETRIES=3
LLM_MODEL=gpt-3.5-turbo
MAX_FILE_SIZE=10485760
MAX_CONCURRENT_REQUESTS=5
```

### Environment Variable Validation

```typescript
// config/env.ts
import { z } from 'zod';

const envSchema = z.object({
  LLM_API_URL: z.string().url('Invalid LLM API URL'),
  LLM_API_KEY: z.string().min(1, 'LLM API key is required'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  LLM_TIMEOUT: z.coerce.number().positive().default(30000),
  LLM_MAX_RETRIES: z.coerce.number().min(0).max(10).default(3),
  LLM_MODEL: z.string().default('gpt-3.5-turbo'),
  MAX_FILE_SIZE: z.coerce.number().positive().default(10485760), // 10MB
  MAX_CONCURRENT_REQUESTS: z.coerce.number().positive().default(5)
});

export const env = envSchema.parse(process.env);

// Validate environment on startup
if (typeof window === 'undefined') {
  try {
    envSchema.parse(process.env);
    console.log('✅ Environment variables validated successfully');
  } catch (error) {
    console.error('❌ Environment validation failed:', error.errors);
    process.exit(1);
  }
}
```

## Build Configuration

### Next.js Configuration

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for Netlify
  output: process.env.DEPLOY_TARGET === 'netlify' ? 'export' : 'standalone',
  
  // Disable image optimization for static export
  images: {
    unoptimized: process.env.DEPLOY_TARGET === 'netlify'
  },
  
  // Environment variables available to the client
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // Webpack configuration
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Custom webpack config for production builds
    if (!dev && !isServer) {
      config.optimization.splitChunks.chunks = 'all';
    }
    
    return config;
  },
  
  // Headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      }
    ];
  },
  
  // API route configuration
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*'
      }
    ];
  }
};

module.exports = nextConfig;
```

### Build Scripts

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "build:vercel": "DEPLOY_TARGET=vercel next build",
    "build:netlify": "DEPLOY_TARGET=netlify next build",
    "build:docker": "docker build -t incident-evaluator .",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:e2e": "playwright test",
    "analyze": "ANALYZE=true next build",
    "export": "next export"
  }
}
```

## Deployment Process

### Automated Deployment (CI/CD)

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run type check
        run: npm run type-check
      
      - name: Run linting
        run: npm run lint
      
      - name: Run tests
        run: npm run test
      
      - name: Run E2E tests
        run: npm run test:e2e

  deploy-vercel:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

### Manual Deployment Steps

#### Vercel Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to production
vercel --prod

# Set environment variables
vercel env add LLM_API_URL production
vercel env add LLM_API_KEY production
```

#### Netlify Deployment

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Build and deploy
npm run build:netlify
netlify deploy --prod --dir=out
```

#### Docker Deployment

```bash
# Build Docker image
docker build -t incident-evaluator .

# Run container
docker run -p 3000:3000 \
  -e LLM_API_URL=https://your-api.com \
  -e LLM_API_KEY=your-key \
  incident-evaluator

# Deploy to container registry
docker tag incident-evaluator your-registry/incident-evaluator:latest
docker push your-registry/incident-evaluator:latest
```

## Performance Optimization

### Build Optimization

```javascript
// Bundle analyzer configuration
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true'
});

module.exports = withBundleAnalyzer({
  // Compression
  compress: true,
  
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60
  },
  
  // Experimental features
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', 'recharts']
  }
});
```

### CDN Configuration

```javascript
// Static asset optimization
const nextConfig = {
  assetPrefix: process.env.NODE_ENV === 'production' 
    ? 'https://cdn.yourcompany.com' 
    : '',
  
  // Cache headers
  async headers() {
    return [
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      }
    ];
  }
};
```

## Monitoring and Health Checks

### Health Check Endpoint

```typescript
// pages/api/health.ts
import type { NextApiRequest, NextApiResponse } from 'next';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    llm: 'up' | 'down';
    database: 'up' | 'down';
  };
  uptime: number;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthStatus>
) {
  const startTime = Date.now();
  
  try {
    // Check LLM service
    const llmStatus = await checkLLMService();
    
    const healthStatus: HealthStatus = {
      status: llmStatus ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        llm: llmStatus ? 'up' : 'down',
        database: 'up' // No database in MVP
      },
      uptime: process.uptime()
    };
    
    const statusCode = healthStatus.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(healthStatus);
    
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        llm: 'down',
        database: 'up'
      },
      uptime: process.uptime()
    });
  }
}

async function checkLLMService(): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.LLM_API_URL}/health`, {
      method: 'GET',
      timeout: 5000
    });
    return response.ok;
  } catch {
    return false;
  }
}
```

### Deployment Verification

```bash
#!/bin/bash
# deploy-verify.sh

DEPLOY_URL=$1

if [ -z "$DEPLOY_URL" ]; then
  echo "Usage: ./deploy-verify.sh <deployment-url>"
  exit 1
fi

echo "Verifying deployment at $DEPLOY_URL..."

# Check health endpoint
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DEPLOY_URL/api/health")
if [ "$HEALTH_STATUS" -eq 200 ]; then
  echo "✅ Health check passed"
else
  echo "❌ Health check failed (HTTP $HEALTH_STATUS)"
  exit 1
fi

# Check main page
MAIN_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DEPLOY_URL")
if [ "$MAIN_STATUS" -eq 200 ]; then
  echo "✅ Main page accessible"
else
  echo "❌ Main page failed (HTTP $MAIN_STATUS)"
  exit 1
fi

# Check API endpoints
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$DEPLOY_URL/api/upload")
if [ "$API_STATUS" -eq 405 ]; then  # Method not allowed is expected for GET
  echo "✅ API endpoints accessible"
else
  echo "❌ API endpoints failed (HTTP $API_STATUS)"
  exit 1
fi

echo "🎉 Deployment verification completed successfully!"
```

## Security Considerations

### Environment Security

```typescript
// Security headers middleware
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // CSP header
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
  );
  
  return response;
}

export const config = {
  matcher: '/((?!api|_next/static|_next/image|favicon.ico).*)'
};
```

### API Key Management

```typescript
// Secure API key handling
function validateApiKey(): boolean {
  const apiKey = process.env.LLM_API_KEY;
  
  if (!apiKey) {
    throw new Error('LLM_API_KEY environment variable is required');
  }
  
  if (apiKey.length < 32) {
    throw new Error('API key appears to be too short');
  }
  
  if (apiKey.includes('example') || apiKey.includes('placeholder')) {
    throw new Error('API key appears to be a placeholder value');
  }
  
  return true;
}

// Call during application startup
if (process.env.NODE_ENV === 'production') {
  validateApiKey();
}
```