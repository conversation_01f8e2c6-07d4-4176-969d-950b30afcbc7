# LLM Integration

## Integration Overview

The application integrates with an in-house Large Language Model (LLM) that provides an OpenAI-compatible API interface. This integration is the core component responsible for evaluating incident ticket quality and providing actionable feedback.

## LLM Service Architecture

### Connection Management

```typescript
class LLMService {
  private baseURL: string;
  private apiKey: string;
  private timeout: number;
  private maxRetries: number;
  
  constructor(config: LLMConfig) {
    this.baseURL = config.apiUrl;
    this.apiKey = config.apiKey;
    this.timeout = config.timeout || 30000;
    this.maxRetries = config.maxRetries || 3;
  }
  
  async evaluateTicket(ticket: IncidentTicket): Promise<LLMEvaluationResponse> {
    const prompt = this.buildEvaluationPrompt(ticket);
    return this.makeRequest(prompt);
  }
}
```

### Request Configuration

```typescript
interface LLMConfig {
  apiUrl: string;              // Base URL for LLM API
  apiKey: string;              // Authentication key
  model?: string;              // Model name (default: 'gpt-3.5-turbo')
  timeout?: number;            // Request timeout in ms (default: 30000)
  maxRetries?: number;         // Max retry attempts (default: 3)
  temperature?: number;        // Response creativity (default: 0.1)
  maxTokens?: number;          // Max response tokens (default: 500)
}
```

## Prompt Engineering

### Evaluation Prompt Template

```typescript
const EVALUATION_PROMPT_TEMPLATE = `
You are an expert incident management analyst. Evaluate the quality of this incident ticket based on completeness, clarity, accuracy, and actionability.

Incident Details:
- Number: {incidentNumber}
- Date: {incidentOpenDate}
- Services: {impactedServices}
- Team: {team}
- Summary: {summary}
- Business Impact: {businessImpact}
- Latest Update: {latestUpdateAndUserInstructions}
- Technical Details: {technicalDetails}

Evaluation Criteria:
1. Completeness (25%): All required information present and comprehensive
2. Clarity (25%): Information is clear, unambiguous, and well-structured
3. Accuracy (25%): Information appears factually correct and consistent
4. Actionability (25%): Provides clear next steps and enables effective response

Provide your evaluation in this exact JSON format:
{
  "ranking": <score from 1-10>,
  "reviewComments": "<detailed feedback addressing each criterion>",
  "confidence": <confidence level from 0.0-1.0>
}

Focus on:
- Business impact and user instructions should be in plain English for non-technical audiences
- Technical details should be precise and accurate for IT professionals
- Identify specific areas for improvement
- Highlight what was done well
`;
```

### Prompt Customization

```typescript
class PromptBuilder {
  static buildEvaluationPrompt(ticket: IncidentTicket): string {
    return EVALUATION_PROMPT_TEMPLATE
      .replace('{incidentNumber}', ticket.incidentNumber)
      .replace('{incidentOpenDate}', ticket.incidentOpenDate)
      .replace('{impactedServices}', ticket.impactedServices)
      .replace('{team}', ticket.team)
      .replace('{summary}', ticket.summary)
      .replace('{businessImpact}', ticket.businessImpact)
      .replace('{latestUpdateAndUserInstructions}', ticket.latestUpdateAndUserInstructions)
      .replace('{technicalDetails}', ticket.technicalDetails);
  }
  
  static buildBatchPrompt(tickets: IncidentTicket[]): string {
    // For batch processing optimization
    return tickets.map((ticket, index) => 
      `Ticket ${index + 1}:\n${this.buildEvaluationPrompt(ticket)}`
    ).join('\n\n---\n\n');
  }
}
```

## Request Processing

### Single Ticket Evaluation

```typescript
async function evaluateTicket(ticket: IncidentTicket): Promise<EvaluatedTicket> {
  const startTime = Date.now();
  
  try {
    const prompt = PromptBuilder.buildEvaluationPrompt(ticket);
    
    const response = await fetch(`${LLM_API_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LLM_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{
          role: 'user',
          content: prompt
        }],
        temperature: 0.1,
        max_tokens: 500,
        response_format: { type: 'json_object' }
      }),
      signal: AbortSignal.timeout(30000)
    });
    
    if (!response.ok) {
      throw new LLMServiceError(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    const evaluation = JSON.parse(data.choices[0].message.content);
    
    return {
      ...ticket,
      ranking: evaluation.ranking,
      reviewComments: evaluation.reviewComments,
      evaluationStatus: 'completed',
      processingTime: Date.now() - startTime
    };
    
  } catch (error) {
    return {
      ...ticket,
      ranking: 0,
      reviewComments: '',
      evaluationStatus: 'failed',
      errorMessage: error.message,
      processingTime: Date.now() - startTime
    };
  }
}
```

### Batch Processing

```typescript
class BatchProcessor {
  private concurrencyLimit: number;
  private retryDelay: number;
  
  constructor(concurrencyLimit = 5, retryDelay = 1000) {
    this.concurrencyLimit = concurrencyLimit;
    this.retryDelay = retryDelay;
  }
  
  async processBatch(
    tickets: IncidentTicket[],
    onProgress?: (progress: ProcessingProgress) => void
  ): Promise<EvaluatedTicket[]> {
    const results: EvaluatedTicket[] = [];
    const chunks = this.chunkArray(tickets, this.concurrencyLimit);
    
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const chunkPromises = chunk.map(ticket => 
        this.evaluateWithRetry(ticket)
      );
      
      const chunkResults = await Promise.allSettled(chunkPromises);
      
      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            ...chunk[index],
            ranking: 0,
            reviewComments: '',
            evaluationStatus: 'failed',
            errorMessage: result.reason.message
          });
        }
      });
      
      // Report progress
      if (onProgress) {
        onProgress({
          total: tickets.length,
          processed: results.length,
          failed: results.filter(r => r.evaluationStatus === 'failed').length,
          percentage: Math.round((results.length / tickets.length) * 100)
        });
      }
      
      // Rate limiting delay between chunks
      if (i < chunks.length - 1) {
        await this.delay(this.retryDelay);
      }
    }
    
    return results;
  }
  
  private async evaluateWithRetry(
    ticket: IncidentTicket,
    maxRetries = 3
  ): Promise<EvaluatedTicket> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await evaluateTicket(ticket);
      } catch (error) {
        lastError = error;
        
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          await this.delay(delay);
        }
      }
    }
    
    throw lastError;
  }
}
```

## Error Handling

### Error Types

```typescript
class LLMServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'LLMServiceError';
  }
}

const LLM_ERROR_CODES = {
  TIMEOUT: 'timeout',
  RATE_LIMIT: 'rate_limit',
  INVALID_RESPONSE: 'invalid_response',
  SERVICE_UNAVAILABLE: 'service_unavailable',
  AUTHENTICATION_FAILED: 'authentication_failed',
  QUOTA_EXCEEDED: 'quota_exceeded'
} as const;
```

### Retry Logic

```typescript
class RetryHandler {
  static shouldRetry(error: LLMServiceError): boolean {
    const retryableCodes = [
      LLM_ERROR_CODES.TIMEOUT,
      LLM_ERROR_CODES.RATE_LIMIT,
      LLM_ERROR_CODES.SERVICE_UNAVAILABLE
    ];
    
    return retryableCodes.includes(error.code as any);
  }
  
  static getRetryDelay(attempt: number, error: LLMServiceError): number {
    if (error.code === LLM_ERROR_CODES.RATE_LIMIT) {
      return 60000; // 1 minute for rate limits
    }
    
    return Math.min(1000 * Math.pow(2, attempt), 30000); // Exponential backoff, max 30s
  }
}
```

## Response Validation

### Response Schema

```typescript
const LLMResponseSchema = z.object({
  ranking: z.number().min(1).max(10),
  reviewComments: z.string().min(10).max(2000),
  confidence: z.number().min(0).max(1).optional()
});

function validateLLMResponse(response: any): LLMEvaluationResponse {
  try {
    return LLMResponseSchema.parse(response);
  } catch (error) {
    throw new LLMServiceError(
      'Invalid LLM response format',
      LLM_ERROR_CODES.INVALID_RESPONSE
    );
  }
}
```

## Performance Optimization

### Connection Pooling

```typescript
class ConnectionPool {
  private connections: Map<string, Connection> = new Map();
  private maxConnections: number;
  
  constructor(maxConnections = 10) {
    this.maxConnections = maxConnections;
  }
  
  async getConnection(): Promise<Connection> {
    // Implement connection pooling logic
    // Reuse existing connections when possible
    // Create new connections up to maxConnections limit
  }
}
```

### Caching Strategy

```typescript
class EvaluationCache {
  private cache: Map<string, CachedEvaluation> = new Map();
  private ttl: number;
  
  constructor(ttlMinutes = 60) {
    this.ttl = ttlMinutes * 60 * 1000;
  }
  
  getCachedEvaluation(ticketHash: string): EvaluatedTicket | null {
    const cached = this.cache.get(ticketHash);
    
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.evaluation;
    }
    
    this.cache.delete(ticketHash);
    return null;
  }
  
  setCachedEvaluation(ticketHash: string, evaluation: EvaluatedTicket): void {
    this.cache.set(ticketHash, {
      evaluation,
      timestamp: Date.now()
    });
  }
}
```

## Monitoring and Logging

### Metrics Collection

```typescript
interface LLMMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  errorsByType: Record<string, number>;
  rateLimitHits: number;
}

class MetricsCollector {
  private metrics: LLMMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    errorsByType: {},
    rateLimitHits: 0
  };
  
  recordRequest(duration: number, success: boolean, errorType?: string): void {
    this.metrics.totalRequests++;
    
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
      if (errorType) {
        this.metrics.errorsByType[errorType] = 
          (this.metrics.errorsByType[errorType] || 0) + 1;
      }
    }
    
    // Update average response time
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + duration) / 
      this.metrics.totalRequests;
  }
}
```