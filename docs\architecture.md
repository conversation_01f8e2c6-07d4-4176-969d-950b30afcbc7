### **Incident Ticket Quality Evaluator Fullstack Architecture Document**

**Version:** 1.0
**Date:** August 31, 2025

### **1. Introduction**

This document outlines the complete fullstack architecture for the Incident Ticket Quality Evaluator, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack. This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

#### **Starter Template or Existing Project**

  * **Decision**: Native Next.js Installation.
  * **Rationale**: To maintain simplicity and avoid any unnecessary abstractions or dependencies from a starter template, the project will be initialized using the standard `create-next-app` command. This ensures the foundation is lean and tailored specifically to the project's core requirements, in line with the "avoid over-engineering" principle.

#### **Change Log**

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| August 31, 2025 | 1.0 | Initial Architecture Draft | Winston (Architect) |

-----

### **2. High-Level Architecture**

#### **Technical Summary**

The system will be a **stateless, full-stack application** built with **Next.js** and run locally. The frontend will be a responsive single-page interface built with **shadcn/ui** and **Tailwind CSS**. The backend will consist of a single **serverless API route** within the Next.js application that handles CSV parsing and orchestrates calls to the in-house LLM API. The entire application will be contained within a **single repository**.

#### **Platform and Infrastructure**

  * **Platform**: **Local Development Machine**.
  * **Rationale**: For the MVP, the application is designed to be run locally, eliminating the need for cloud infrastructure and simplifying the setup.

#### **Repository Structure**

  * **Structure**: **Monorepo**.
  * **Rationale**: A single repository is the most straightforward approach for a unified Next.js application, simplifying dependency management and the development workflow for the MVP.

#### **High-Level Architecture Diagram**

```mermaid
graph TD
    A[User's Browser] --> B[Local Next.js Application];
    B -- 1. Upload CSV --> B;
    B -- 2. API Call with Incident Data --> C[In-House LLM API];
    C -- 3. Returns Evaluation --> B;
    B -- 4. Displays Results --> A;
```

#### **Architectural Patterns**

  * **Stateless Architecture**: The core principle for the MVP. The application will not store any user data, session information, or results between requests.
  * **Serverless Functions**: We will leverage the native Next.js API Routes for our backend logic, which run as serverless functions.
  * **Component-Based UI**: The frontend will be built using a modular, component-based architecture as is standard with React.

-----

### **3. Tech Stack**

| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Frontend Language** | TypeScript | `~5.x` | Language for frontend development | Provides type safety and scalability. |
| **Frontend Framework** | Next.js | `~14.x` | Primary application framework | Handles both UI and backend logic seamlessly. |
| **UI Component Library**| shadcn/ui | `Latest` | Foundational UI components | Accessible, customizable, and not a dependency. |
| **State Management** | Zustand | `~4.x` | Client-side state management | Lightweight and simple; avoids Redux boilerplate. |
| **Backend Language** | TypeScript | `~5.x` | Language for backend API routes | Keeps the entire codebase consistent. |
| **Backend Framework** | Next.js API Routes | `~14.x` | Framework for serverless backend | Native to the framework, simple to use. |
| **API Style** | REST | `N/A` | API communication standard | Simple, well-understood, and easy to implement. |
| **Database** | N/A | `N/A` | N/A | The MVP is stateless as per the PRD. |
| **File Storage** | Local Filesystem | `N/A` | Temporary storage for uploads | Simplest approach for a local application. |
| **Authentication** | N/A | `N/A` | N/A | Out of scope for the MVP. |
| **Frontend Testing** | Vitest & RTL | `Latest` | Component and unit testing | Fast, modern, and the standard for React. |
| **Backend Testing** | Vitest | `Latest` | API route unit testing | Keeps the testing stack consistent. |
| **E2E Testing** | N/A | `N/A` | N/A | Deferred post-MVP as per the PRD. |
| **CI/CD** | N/A (Manual) | `N/A` | N/A | CI/CD is not required for the local MVP. |
| **Monitoring** | Console / Terminal | `N/A` | Development monitoring & logging | Standard for local development; no setup needed. |
| **Logging** | Console / Terminal | `N/A` | Serverless function logging | Standard for local Next.js development. |
| **CSS Framework** | Tailwind CSS | `~3.x` | Utility-first CSS styling | Works natively with shadcn/ui. |

-----

### **4. Data Models**

#### **IncidentTicket**

  * **Purpose**: To represent a single incident ticket record, from its initial state in the CSV to its final state with the LLM evaluation.
  * **TypeScript Interface**:
    ```typescript
    export interface IncidentTicket {
      // Fields from CSV
      id: string;
      ImpactDate: string;
      Service: string;
      ProblemService: string;
      Summary: string;
      BusinessImpact: string;
      Instructions: string;
      TechnicalDetails: string;

      // Fields added by LLM Evaluation
      Ranking?: 'Poor' | 'Below Average' | 'Average' | 'Good' | 'Excellent' | 'Error';
      'Review Comments'?: string;

      // Processing metadata
      processingStatus: 'pending' | 'in-progress' | 'complete' | 'failed';
      errorMessage?: string;
    }
    ```

-----

### **5. API Specification**

This API is designed in the **REST** style and documented using the OpenAPI 3.0 standard.

```yaml
openapi: 3.0.0
info:
  title: Incident Ticket Quality Evaluator API
  version: 1.0.0
  description: API for uploading incident ticket CSVs and retrieving quality evaluations.
servers:
  - url: /
    description: Local server

paths:
  /api/evaluate:
    post:
      summary: Uploads a CSV for evaluation and starts the processing job.
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The CSV file containing incident tickets.
      responses:
        '202':
          description: Accepted. The processing job has been started.
          content:
            application/json:
              schema:
                type: object
                properties:
                  jobId:
                    type: string
        '400':
          description: Bad Request. Invalid file type or malformed request.

  /api/evaluate/status/{jobId}:
    get:
      summary: Retrieves the status and results of a processing job.
      parameters:
        - name: jobId
          in: path
          required: true
          schema:
            type: string
          description: The ID of the processing job.
      responses:
        '200':
          description: OK. Returns the current status and results.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [processing, complete, failed]
                  progress:
                    type: object
                    properties: { total: { type: integer }, processed: { type: integer }, succeeded: { type: integer }, failed: { type: integer } }
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/IncidentTicket'
        '404':
          description: Not Found. The specified jobId does not exist.

components:
  schemas:
    IncidentTicket:
      type: object
      properties:
        # Properties match the TypeScript interface
```

-----

### **6. Components**

  * **1. Frontend UI**: Renders the UI, manages client-side state, and communicates with the Backend API.
  * **2. Backend API**: Exposes the REST API, validates requests, and initiates processing jobs.
  * **3. Job Management Service**: Manages the lifecycle of asynchronous jobs in-memory.
  * **4. CSV Processing Service**: Contains the core business logic for parsing the CSV and orchestrating the LLM evaluation.
  * **5. LLM Service**: Encapsulates all communication with the in-house LLM.

#### **Component Interaction Diagram**

```mermaid
graph TD
    User -- Interacts with --> A[Frontend UI];
    A -- HTTP Request --> B[Backend API];
    B -- Creates Job --> C[Job Management Service];
    B -- Initiates Processing --> D[CSV Processing Service];
    D -- Evaluates each row --> E[LLM Service];
    E -- Calls --> F[In-House LLM API];
    D -- Updates Status --> C;
    A -- Polls for Status/Results --> B;
    B -- Retrieves Status --> C;
```

-----

### **7. External APIs**

#### **In-House LLM API**

  * **Purpose**: To provide a quality evaluation for the text of a single incident ticket.
  * **Documentation**: To be provided.
  * **Authentication**: Assumed to be via an API Key in an `Authorization` header.
  * **Rate Limits**: To be determined.
  * **Key Endpoints Used**: Assumed to be `POST /v1/chat/completions`.

-----

### **8. Core Workflows**

```mermaid
sequenceDiagram
    participant User
    participant Frontend UI
    participant Backend API
    participant Job Management Service
    participant CSV Processing Service
    participant LLM Service
    participant In-House LLM API

    User->>+Frontend UI: 1. Uploads CSV file
    Frontend UI->>+Backend API: 2. POST /api/evaluate (file)
    Backend API->>+Job Management Service: 3. createJob()
    Job Management Service-->>-Backend API: 4. Returns jobId
    Backend API-->>-Frontend UI: 5. 202 Accepted (jobId)
    Note over Backend API, CSV Processing Service: Process starts asynchronously
    Backend API->>CSV Processing Service: 6. processCsvFile(file, jobId)

    loop Every few seconds
        Frontend UI->>+Backend API: 7. GET /api/evaluate/status/{jobId}
        Backend API->>+Job Management Service: 8. getJob(jobId)
        Job Management Service-->>-Backend API: 9. Returns current status & results
        Backend API-->>-Frontend UI: 10. Returns status & partial results
    end

    CSV Processing Service->>+LLM Service: 11. evaluateTicket(rowData)
    LLM Service->>+In-House LLM API: 12. POST /v1/chat/completions
    In-House LLM API-->>-LLM Service: 13. Returns evaluation
    LLM Service-->>-CSV Processing Service: 14. Returns evaluation
    CSV Processing Service->>+Job Management Service: 15. updateJobProgress(jobId, result)
```

-----

### **9. Frontend Architecture**

  * **Component Architecture**: Feature-based organization within `src/components/features/`.
  * **State Management Architecture**: A single, global Zustand store will manage UI state.
  * **Routing Architecture**: A single page at `src/app/page.tsx` using the Next.js App Router.
  * **Frontend Services Layer**: A dedicated service layer (`src/services/`) will handle all API communication, abstracting `fetch` calls from the UI components.

-----

### **10. Backend Architecture**

  * **Service Architecture**: Serverless functions using Next.js API Routes located in `src/app/api/`.
  * **Database Architecture**: Not applicable for the stateless MVP.
  * **Authentication and Authorization**: Not applicable for the MVP.

-----

### **11. Unified Project Structure**

```plaintext
/incident-quality-evaluator
├── .eslintrc.json
├── next.config.js
├── package.json
├── README.md
├── tsconfig.json
└── src/
    ├── app/
    │   ├── page.tsx          # Frontend UI
    │   └── api/              # Backend API
    ├── components/
    │   ├── ui/               # shadcn/ui components
    │   └── features/         # App-specific components
    ├── lib/
    │   ├── api-client.ts     # Frontend fetch wrapper
    │   ├── types.ts          # Shared types
    │   └── server/           # Backend logic modules
    └── services/
        └── evaluation.service.ts # Frontend service
```

-----

### **12. Development Workflow**

  * **Prerequisites**: Node.js (`~20.x`), npm/yarn, Git.
  * **Setup**: `npm install`, then create and populate `.env.local` with `LLM_API_BASE_URL` and `LLM_API_KEY`.
  * **Commands**: `npm run dev`, `npm run build`, `npm run test`.

-----

### **13. Deployment Architecture**

  * **Strategy**: Local execution only for the MVP. No cloud deployment is planned.

-----

### **14. Security and Performance**

  * **Security**: Focus is on server-side validation of the uploaded CSV file.
  * **Performance**: The primary strategy is the asynchronous API pattern to handle long-running LLM calls and UI virtualization/pagination to handle large result sets.

-----

### **15. Testing Strategy**

  * **MVP Focus**: A strong foundation of unit tests (Vitest) and component tests (React Testing Library) for both frontend and backend logic. E2E tests are deferred.

-----

### **16. Coding Standards**

  * **Key Rules**: Enforce the use of the shared `IncidentTicket` type, use the service layer for all API calls, and wrap all async operations in `try/catch` blocks.

-----

### **17. Error Handling Strategy**

  * **Pattern**: The backend API will return a structured JSON error object, and the frontend will display a user-friendly message.

-----

### **18. Monitoring and Observability**

  * **Strategy**: For the local MVP, all logging and monitoring will be done via the developer's console/terminal.

-----

### **19. Checklist Results Report**

  * This document is now ready for a final validation using the `architect-checklist`.

### **20. Next Steps**

  * This architecture document is complete. It should be handed off to the **Product Owner** for final review and then to the **Development Agent** to begin implementation.