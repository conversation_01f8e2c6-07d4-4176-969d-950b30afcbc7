# API Design

## API Architecture Overview

The application uses Next.js API routes to provide a RESTful interface for all backend operations. All APIs follow consistent patterns for request/response handling, error management, and status codes.

## Core API Endpoints

### File Upload API

#### POST /api/upload

Handles CSV file upload and initial validation.

**Request:**
```typescript
// Content-Type: multipart/form-data
FormData {
  file: File; // CSV file
}
```

**Response:**
```typescript
// Success (200)
{
  success: true,
  sessionId: string,
  fileName: string,
  totalTickets: number,
  validationErrors: string[]
}

// Error (400)
{
  success: false,
  error: string,
  details?: any
}
```

**Validation Rules:**
- File must be CSV format (MIME type validation)
- File size must not exceed 10MB
- Must contain all required columns
- At least one data row must be present

### Processing API

#### POST /api/process

Initiates batch processing of uploaded tickets.

**Request:**
```typescript
{
  sessionId: string,
  batchSize?: number // Default: 5, Max: 10
}
```

**Response:**
```typescript
// Success (200)
{
  success: true,
  sessionId: string,
  status: 'started',
  message: string
}

// Already Processing (409)
{
  success: false,
  error: 'Session already processing',
  currentStatus: string
}
```

**Processing Logic:**
- Validates session exists and is ready for processing
- Initiates background processing with specified batch size
- Returns immediately with processing status
- Handles concurrent request prevention

### Status Monitoring API

#### GET /api/status/[sessionId]

Returns current processing status for a session.

**Response:**
```typescript
// Success (200)
{
  sessionId: string,
  status: 'processing' | 'completed' | 'failed',
  progress: {
    total: number,
    processed: number,
    failed: number,
    percentage: number
  },
  estimatedTimeRemaining?: number, // milliseconds
  lastUpdated: string, // ISO timestamp
  tickets?: EvaluatedTicket[] // Only when completed
}

// Not Found (404)
{
  error: 'Session not found',
  sessionId: string
}
```

**Polling Guidelines:**
- Client should poll every 2-3 seconds during processing
- Implement exponential backoff for failed requests
- Stop polling when status is 'completed' or 'failed'

### Export API

#### POST /api/export

Generates and returns enhanced CSV with evaluation results.

**Request:**
```typescript
{
  sessionId: string,
  includeFailedTickets?: boolean, // Default: false
  format?: 'csv' | 'json' // Default: 'csv'
}
```

**Response:**
```typescript
// Success (200)
// Content-Type: text/csv or application/json
// Content-Disposition: attachment; filename="results-{timestamp}.csv"

// CSV format includes all original columns plus:
// - ranking: number
// - review_comments: string
// - evaluation_status: string
// - processing_time_ms: number
```

### Health Check API

#### GET /api/health

System health and dependency status check.

**Response:**
```typescript
{
  status: 'healthy' | 'degraded' | 'unhealthy',
  timestamp: string,
  services: {
    llm: {
      status: 'up' | 'down',
      responseTime?: number,
      lastChecked: string
    },
    memory: {
      usage: number, // percentage
      available: number // bytes
    }
  },
  version: string
}
```

## Error Handling Standards

### HTTP Status Codes

- **200 OK**: Successful operation
- **400 Bad Request**: Invalid request data or parameters
- **401 Unauthorized**: Authentication required (if implemented)
- **404 Not Found**: Resource not found (session, file, etc.)
- **409 Conflict**: Resource conflict (already processing)
- **413 Payload Too Large**: File size exceeds limit
- **422 Unprocessable Entity**: Valid request but business logic error
- **429 Too Many Requests**: Rate limiting (if implemented)
- **500 Internal Server Error**: Unexpected server error
- **502 Bad Gateway**: LLM service unavailable
- **503 Service Unavailable**: System overloaded

### Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  error: string;           // Human-readable error message
  code?: string;           // Machine-readable error code
  details?: any;           // Additional error context
  timestamp: string;       // ISO timestamp
  requestId?: string;      // Request tracking ID
}
```

### Common Error Codes

- `INVALID_FILE_FORMAT`: Uploaded file is not valid CSV
- `FILE_TOO_LARGE`: File exceeds maximum size limit
- `MISSING_COLUMNS`: Required CSV columns are missing
- `SESSION_NOT_FOUND`: Processing session does not exist
- `SESSION_EXPIRED`: Session has expired (if TTL implemented)
- `ALREADY_PROCESSING`: Session is currently being processed
- `LLM_SERVICE_ERROR`: External LLM service error
- `RATE_LIMIT_EXCEEDED`: Too many requests to LLM service
- `PROCESSING_TIMEOUT`: Processing took too long
- `INVALID_SESSION_STATE`: Session in unexpected state

## Request/Response Middleware

### Request Validation

```typescript
// Zod schema validation for all API endpoints
const validateRequest = (schema: ZodSchema) => {
  return (req: NextRequest) => {
    const result = schema.safeParse(req.body);
    if (!result.success) {
      throw new ValidationError(result.error.issues);
    }
    return result.data;
  };
};
```

### Response Formatting

```typescript
// Consistent response wrapper
const apiResponse = {
  success: <T>(data: T, status = 200) => {
    return NextResponse.json(
      { success: true, ...data },
      { status }
    );
  },
  
  error: (message: string, status = 400, code?: string) => {
    return NextResponse.json(
      {
        success: false,
        error: message,
        code,
        timestamp: new Date().toISOString()
      },
      { status }
    );
  }
};
```

### Rate Limiting

```typescript
// Simple in-memory rate limiting
const rateLimiter = {
  requests: new Map<string, number[]>(),
  
  check: (identifier: string, limit = 100, window = 60000) => {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // Remove old requests outside window
    const validRequests = requests.filter(time => now - time < window);
    
    if (validRequests.length >= limit) {
      throw new RateLimitError('Too many requests');
    }
    
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
  }
};
```

## Authentication & Security

### API Key Authentication (Optional)

```typescript
// If API key authentication is required
const authenticateRequest = (req: NextRequest) => {
  const apiKey = req.headers.get('x-api-key');
  
  if (!apiKey || !isValidApiKey(apiKey)) {
    throw new AuthenticationError('Invalid API key');
  }
};
```

### CORS Configuration

```typescript
// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGINS || '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key',
  'Access-Control-Max-Age': '86400'
};
```

## API Documentation

### OpenAPI Specification

The API should be documented using OpenAPI 3.0 specification for automatic documentation generation and client SDK creation.

### Example Usage

```typescript
// Complete workflow example
const processIncidentTickets = async (file: File) => {
  // 1. Upload file
  const uploadResponse = await fetch('/api/upload', {
    method: 'POST',
    body: new FormData().append('file', file)
  });
  
  const { sessionId } = await uploadResponse.json();
  
  // 2. Start processing
  await fetch('/api/process', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ sessionId, batchSize: 5 })
  });
  
  // 3. Poll for status
  let status = 'processing';
  while (status === 'processing') {
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const statusResponse = await fetch(`/api/status/${sessionId}`);
    const statusData = await statusResponse.json();
    status = statusData.status;
  }
  
  // 4. Export results
  const exportResponse = await fetch('/api/export', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ sessionId })
  });
  
  return exportResponse.blob();
};
```