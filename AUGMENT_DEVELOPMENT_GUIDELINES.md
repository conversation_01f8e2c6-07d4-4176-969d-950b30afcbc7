# Augment User Guidelines: Iterative Development Workflow

## Overview
This guide establishes a structured, iterative development workflow for building software with Augment Agent. The methodology emphasizes working software delivery through incremental, testable improvements while maintaining code quality and architectural integrity.

## Core Development Philosophy

### 1. Agile Methodology
- **Continuous Feedback Loops**: Each iteration incorporates user feedback and learning
- **Adaptive Planning**: Adjust approach based on results from previous iterations
- **Collaborative Development**: Maintain open communication between user and AI agent

### 2. Incremental Delivery
- **Small Work Units**: Break features into 20-30 minute development chunks
- **Independent Tasks**: Each task should be testable and committable in isolation
- **Demonstrable Progress**: Every iteration produces visible, functional improvements

### 3. Working Software First
- **Functional Priority**: Prioritize working functionality over perfect architecture
- **Immediate Value**: Each iteration delivers usable features
- **Continuous Integration**: Maintain application functionality throughout development

### 4. Progressive Enhancement
- **Build Upon Success**: Enhance existing working features rather than replacing them
- **Evolutionary Architecture**: Allow system design to emerge through iterations
- **Risk Mitigation**: Minimize disruption to proven functionality

## Structured Workflow Process

### Phase 1: Requirements Analysis

#### User Responsibilities:
- Provide specific feature requirements with clear acceptance criteria
- Define success metrics and expected outcomes
- Identify any constraints or dependencies

#### AI Agent Actions:
- Analyze requirements for completeness and clarity
- Ask clarifying questions to resolve ambiguities
- Confirm understanding before proceeding

#### Deliverables:
- Clear, unambiguous requirement specification
- Defined acceptance criteria
- Identified assumptions and constraints

### Phase 2: Technical Design

#### Design Documentation Must Include:
1. **Architecture Decisions**
   - Component structure and relationships
   - Data flow and state management
   - Integration patterns with existing code

2. **Technical Rationale**
   - Why specific approaches were chosen
   - Trade-offs considered
   - Alternative solutions evaluated

3. **Implementation Strategy**
   - Step-by-step development approach
   - Dependencies and prerequisites
   - Risk mitigation strategies

4. **Testing Strategy**
   - Unit test requirements
   - Integration test approach
   - Validation criteria

#### AI Agent Process:
- Use `codebase-retrieval` to understand existing architecture
- Use `git-commit-retrieval` to learn from similar past implementations
- Create detailed technical design before implementation

### Phase 3: Task Breakdown

#### Task Management Guidelines:
- Use Augment's task management tools for complex work
- Create tasks using `add_tasks` with clear descriptions
- Update task states using `update_tasks` as work progresses

#### Task Characteristics:
Each task must be:
- **Independently Testable**: Can be validated in isolation
- **Committable**: Represents a complete unit of work
- **Time-Bounded**: Approximately 20-30 minutes of development
- **Clear Success Criteria**: Unambiguous completion definition

#### Task States:
- `[ ]` Not Started: Tasks not yet begun
- `[/]` In Progress: Currently active tasks
- `[x]` Complete: User-validated completed tasks
- `[-]` Cancelled: No longer relevant tasks

### Phase 4: Implementation

#### Code Quality Standards:
1. **TypeScript Usage**
   - Proper type definitions for all functions and components
   - Avoid `any` types unless absolutely necessary
   - Use interfaces and type unions appropriately

2. **Component Architecture**
   - Eliminate code duplication through reusable components
   - Maintain clear separation of concerns
   - Follow modern UI/UX design patterns

3. **Dependency Management**
   - Always use package managers (npm, yarn, pip, etc.)
   - Never manually edit package configuration files
   - Let package managers handle version resolution

#### Implementation Process:
1. **Sequential Task Execution**
   - Complete tasks in logical dependency order
   - Maintain application functionality after each task
   - Write tests before marking tasks complete

2. **Continuous Integration**
   - Commit each completed task individually
   - Use descriptive commit messages
   - Ensure application remains functional after each commit

3. **Testing Requirements**
   - Write unit tests for new functionality
   - Run tests before task completion
   - Suggest test execution to users after code changes

#### Simplicity First Approach:
- **Essential Features Only**: Implement minimum viable functionality
- **Readable Code**: Prefer explicit, understandable patterns
- **Avoid Premature Optimization**: Focus on working solutions first
- **Establish Foundation**: Build solid base before adding complexity

#### Deferred Complexity:
Postpone until core functionality is proven:
- Performance optimization
- Advanced error handling
- Caching strategies
- Scalability enhancements
- Complex abstractions

### Phase 5: Review and Validation

#### Demonstration Requirements:
- Show working functionality to user
- Explain implementation decisions
- Highlight any limitations or known issues

#### Feedback Collection:
- Document user feedback systematically
- Identify areas for improvement
- Plan next iteration priorities

#### Iteration Planning:
- Review completed tasks and outcomes
- Adjust approach based on learnings
- Define next iteration scope

## Best Practices for Users

### Effective Communication:
- Provide specific, actionable requirements
- Give clear feedback on delivered functionality
- Ask questions when implementation approaches are unclear

### Requirement Definition:
- Include acceptance criteria with each request
- Specify any constraints or preferences
- Provide examples when helpful

### Feedback Guidelines:
- Test delivered functionality thoroughly
- Report issues with specific reproduction steps
- Suggest improvements for future iterations

## Best Practices for AI Agent

### Information Gathering:
- Always use `codebase-retrieval` before making edits
- Use `git-commit-retrieval` to learn from past implementations
- Ask for clarification rather than making assumptions

### Code Display:
- Wrap code excerpts in `<augment_code_snippet>` XML tags
- Include `path=` and `mode="EXCERPT"` attributes
- Keep excerpts brief (under 10 lines) for readability

### Error Recovery:
- Ask for user help when going in circles
- Recognize when additional information is needed
- Suggest alternative approaches when stuck

## Success Metrics

### Iteration Success:
- Working functionality delivered
- All tests passing
- User acceptance criteria met
- Code quality standards maintained

### Overall Project Success:
- Consistent progress toward goals
- Maintainable, well-structured codebase
- User satisfaction with delivered features
- Sustainable development velocity

## Conclusion

This iterative development workflow ensures consistent delivery of high-quality, working software while maintaining flexibility to adapt based on user feedback and changing requirements. The key to success is maintaining discipline in following the structured process while remaining responsive to user needs and technical discoveries.

Remember: The primary objective is establishing a solid, incrementally improvable foundation rather than building a complex system upfront.
