# Flash QA - AI-Powered Incident Analysis

A streamlined Next.js application for instant AI-powered incident ticket categorization and quality analysis.

## Features

- **Simple Upload**: Drag-and-drop CSV file upload with validation
- **Instant AI Analysis**: Real-time LLM evaluation of each incident ticket
- **Immediate Results**: Synchronous processing with instant result display
- **Smart Categorization**: AI-powered categorization into Network, Hardware, Software, Security, or Application
- **Team Assignment**: Automatic team assignment based on predicted categories
- **Confidence Scoring**: AI confidence scores for each prediction

## Simplified Architecture

This application follows a streamlined, synchronous architecture:

1. **File Upload**: User uploads CSV file
2. **LLM Evaluation**: Each record is immediately processed using LLM
3. **Results Display**: Results are displayed directly without polling or background jobs

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes (single endpoint)
- **AI Integration**: OpenAI-compatible LLM API
- **File Processing**: Papa Parse for CSV handling
- **Validation**: <PERSON><PERSON> for schema validation

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Access to an OpenAI-compatible LLM API

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd flash-qa
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your LLM API configuration:
```env
LLM_API_URL=https://your-llm-endpoint.com
LLM_API_KEY=your-api-key
LLM_TIMEOUT=30000
LLM_MAX_RETRIES=3
MAX_FILE_SIZE=10485760
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

### Simple 3-Step Process:

1. **Upload**: Select your CSV file containing incident tickets
2. **Wait**: The system processes each ticket with AI evaluation (may take a few moments)
3. **Review**: View instant results with AI categorization, confidence scores, and team assignments

### CSV Format Requirements

Your CSV file must include these columns:
- `id`: Unique ticket identifier
- `ImpactDate`: When the incident occurred  
- `Service`: Primary service affected
- `ProblemService`: Service where the problem originated
- `Summary`: Brief description of the incident
- `BusinessImpact`: Impact on business operations
- `Instructions`: Resolution instructions
- `TechnicalDetails`: Technical information about the incident

## API

**Single Endpoint Architecture:**
- `POST /api/upload` - Upload CSV file and get immediate AI-evaluated results

## Development

### Running Tests

```bash
npm test
```

### Building for Production

```bash
npm run build
npm start
```

### Code Quality

```bash
npm run lint
npm run type-check
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `LLM_API_URL` | Your LLM API endpoint URL | Yes |
| `LLM_API_KEY` | API key for LLM service | Yes |
| `LLM_TIMEOUT` | Request timeout in milliseconds | No (default: 30000) |
| `LLM_MAX_RETRIES` | Max retry attempts for failed requests | No (default: 3) |
| `MAX_FILE_SIZE` | Maximum file size in bytes | No (default: 10MB) |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
