# Technology Stack

## Frontend Technologies

### Core Framework
- **Next.js 14+**: React framework with App Router for full-stack development
- **React 18+**: Component-based UI library with hooks and context
- **TypeScript**: Type-safe JavaScript for better development experience

### UI and Styling
- **Tailwind CSS**: Utility-first CSS framework for responsive design
- **Headless UI**: Unstyled, accessible UI components
- **Heroicons**: SVG icon library optimized for Tailwind CSS
- **Recharts**: Composable charting library for React

### State Management
- **React Context**: Built-in state management for global application state
- **useState/useReducer**: Local component state management
- **Custom Hooks**: Reusable stateful logic extraction

## Backend Technologies

### Server Framework
- **Next.js API Routes**: Server-side API endpoints within the Next.js application
- **Node.js Runtime**: JavaScript runtime for server-side execution

### Data Processing
- **Papa Parse**: Powerful CSV parsing library with streaming support
- **Zod**: TypeScript-first schema validation library
- **Lodash**: Utility library for data manipulation

### HTTP Client
- **Fetch API**: Native HTTP client for LLM API communication
- **Axios** (alternative): Promise-based HTTP client with interceptors

## Development Tools

### Code Quality
- **ESLint**: JavaScript/TypeScript linting
- **Prettier**: Code formatting
- **Husky**: Git hooks for pre-commit validation
- **lint-staged**: Run linters on staged files

### Testing Framework
- **Jest**: JavaScript testing framework
- **React Testing Library**: Testing utilities for React components
- **Playwright**: End-to-end testing framework
- **MSW (Mock Service Worker)**: API mocking for testing

### Build and Development
- **Webpack**: Module bundler (built into Next.js)
- **SWC**: Fast TypeScript/JavaScript compiler
- **PostCSS**: CSS processing tool
- **Autoprefixer**: CSS vendor prefixing

## External Integrations

### LLM Integration
- **OpenAI-Compatible API**: In-house LLM with OpenAI-compatible interface
- **Custom HTTP Client**: Tailored client for LLM communication
- **Retry Logic**: Exponential backoff for failed requests

### File Processing
- **Browser File API**: Client-side file handling
- **Streaming Processing**: Handle large files efficiently
- **MIME Type Validation**: Ensure uploaded files are CSV format

## Deployment and Infrastructure

### Deployment Platforms
- **Vercel**: Recommended platform for Next.js applications
- **Netlify**: Alternative deployment platform
- **Docker**: Containerization for custom deployment

### Environment Configuration
- **Environment Variables**: Configuration management
- **dotenv**: Local development environment setup
- **Config Validation**: Runtime configuration validation

## Performance Optimization

### Frontend Optimization
- **Code Splitting**: Automatic code splitting with Next.js
- **Image Optimization**: Next.js built-in image optimization
- **Bundle Analysis**: Webpack bundle analyzer for size optimization
- **Lazy Loading**: Component lazy loading for better performance

### Backend Optimization
- **Request Batching**: Efficient LLM API request batching
- **Memory Management**: Proper cleanup of large data structures
- **Streaming Responses**: Stream large responses to client
- **Compression**: Response compression for faster data transfer

## Development Workflow

### Version Control
- **Git**: Distributed version control system
- **GitHub**: Code repository and collaboration platform
- **Conventional Commits**: Standardized commit message format

### Package Management
- **npm**: Node.js package manager
- **package-lock.json**: Dependency version locking
- **Security Auditing**: Regular dependency security checks

### Monitoring and Logging
- **Console Logging**: Development and debugging
- **Error Boundaries**: React error handling
- **Performance Monitoring**: Web Vitals tracking
- **Analytics**: User interaction tracking (optional)