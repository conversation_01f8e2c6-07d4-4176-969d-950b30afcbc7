# System Architecture

## High-Level Architecture

The Incident Ticket Quality Evaluator follows a three-tier architecture:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   File Upload   │  │   Results View  │  │   Charts    │ │
│  │   Component     │  │   Component     │  │  Component  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   API Routes    │  │   Business      │  │   State     │ │
│  │   (/api/*)      │  │   Logic         │  │ Management  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Integration Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   LLM API       │  │   CSV Parser    │  │   File      │ │
│  │   Client        │  │   Service       │  │  Handler    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Component Architecture

### Frontend Components

#### Core Components
- **FileUploadComponent**: Handles CSV file selection and validation
- **ProcessingStatusComponent**: Shows real-time progress during LLM evaluation
- **ResultsTableComponent**: Displays paginated results with search and filter capabilities
- **SummaryStatsComponent**: Shows processing statistics and metrics
- **ScoreDistributionChart**: Visualizes score distribution with team filtering
- **ExportComponent**: Handles CSV export functionality

#### Layout Components
- **MainLayout**: Primary application layout with navigation
- **LoadingSpinner**: Reusable loading indicator
- **ErrorBoundary**: Global error handling component
- **Toast**: User notification system

### Backend API Routes

#### Core API Endpoints
- **POST /api/upload**: File upload and initial validation
- **POST /api/process**: Batch processing of incident tickets
- **GET /api/status/{sessionId}**: Processing status polling
- **POST /api/export**: Generate enhanced CSV export
- **GET /api/health**: System health check

#### Service Layer
- **LLMService**: Handles communication with in-house LLM
- **CSVService**: Parsing and validation of CSV files
- **ProcessingService**: Orchestrates ticket evaluation workflow
- **ValidationService**: Input validation and sanitization

## Data Flow Architecture

### Request Processing Flow

1. **File Upload Phase**
   ```
   User → FileUpload → /api/upload → CSV Validation → Session Creation
   ```

2. **Processing Phase**
   ```
   Frontend → /api/process → Batch Processing → LLM API Calls → Progress Updates
   ```

3. **Results Phase**
   ```
   Results Display → Search/Filter → Export → /api/export → Enhanced CSV
   ```

### State Management

- **Client State**: React useState and useContext for UI state
- **Server State**: Temporary session-based storage during processing
- **Cache Strategy**: No persistent caching (stateless architecture)

## Security Architecture

### Data Protection
- **No Persistent Storage**: All data processed in memory only
- **Session Isolation**: Each upload session is isolated
- **Input Sanitization**: Comprehensive validation of all inputs
- **API Security**: Secure communication with LLM service

### Error Handling
- **Graceful Degradation**: System continues operating with partial failures
- **Retry Logic**: Automatic retry for transient LLM API failures
- **User Feedback**: Clear error messages without exposing system details
- **Logging**: Structured logging without sensitive data exposure