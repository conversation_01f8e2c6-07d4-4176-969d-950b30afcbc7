import { z } from 'zod';
import { IncidentTicket, UploadErrorType, UploadError } from '@/types';

// Environment configuration
export const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB default

/**
 * Zod schema for incident ticket validation
 * Based on story requirements - all fields are required and must be non-empty strings
 */
export const IncidentTicketSchema = z.object({
  'id': z.string().min(1, 'Incident number is required'),
  'ImpactDate': z.string().min(1, 'Open date is required'),
  'Service': z.string().min(1, 'Impacted service required'),
  'ProblemService': z.string().min(1, 'Problem service is required'),
  'Summary': z.string().min(1, 'Summary is required'),
  'BusinessImpact': z.string().min(1, 'Business impact is required'),
  'Instructions': z.string().min(1, 'Instructions are required'),
  'TechnicalDetails': z.string().min(1, 'Technical details are required')
});

/**
 * Required CSV columns as per story acceptance criteria
 */
export const REQUIRED_COLUMNS = [
  'id',
  'ImpactDate',
  'Service',
  'ProblemService',
  'Summary',
  'BusinessImpact',
  'Instructions',
  'TechnicalDetails'
] as const;

/**
 * Validate file basic properties (size, type)
 */
export function validateFile(file: File): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    errors.push(`File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (${(MAX_FILE_SIZE / 1024 / 1024).toFixed(2)}MB)`);
  }

  // Check MIME type
  if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {
    errors.push('File must be in CSV format');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate CSV headers against required columns
 */
export function validateCSVHeaders(headers: string[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const normalizedHeaders = headers.map(h => h.trim());
  
  // Check for missing required columns
  const missingColumns = REQUIRED_COLUMNS.filter(col => !normalizedHeaders.includes(col));
  
  if (missingColumns.length > 0) {
    errors.push(`Missing required columns: ${missingColumns.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate individual incident ticket data
 */
export function validateIncidentTicket(data: any, rowIndex: number): { isValid: boolean; errors: string[] } {
  try {
    IncidentTicketSchema.parse(data);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => 
        `Row ${rowIndex + 1}: ${err.path.join('.')} - ${err.message}`
      );
      return { isValid: false, errors };
    }
    return { 
      isValid: false, 
      errors: [`Row ${rowIndex + 1}: Validation failed`] 
    };
  }
}

/**
 * Validate entire CSV data array
 */
export function validateCSVData(data: any[]): { validTickets: IncidentTicket[]; errors: string[] } {
  const validTickets: IncidentTicket[] = [];
  const errors: string[] = [];

  data.forEach((row, index) => {
    const validation = validateIncidentTicket(row, index);
    if (validation.isValid) {
      validTickets.push(row as IncidentTicket);
    } else {
      errors.push(...validation.errors);
    }
  });

  return { validTickets, errors };
}

/**
 * Create upload error with proper typing
 */
export function createUploadError(type: UploadErrorType, message: string, details?: any): UploadError {
  return new UploadError(type, message, details);
}

/**
 * Generate unique session ID
 */
export function generateSessionId(): string {
  return crypto.randomUUID();
}