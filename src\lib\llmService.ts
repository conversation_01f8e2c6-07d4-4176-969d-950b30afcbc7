import { IncidentTicket } from '@/types';

/**
 * LLM Evaluation Response Interface
 * Based on MVP requirements for quality evaluation
 */
export interface LLMEvaluationResponse {
  ranking: 'Poor' | 'Below Average' | 'Average' | 'Good' | 'Excellent' | 'Error';
  reviewComments: string;
}

/**
 * LLM Service Configuration
 */
interface LLMConfig {
  apiUrl: string;
  apiKey: string;
  timeout?: number;
  maxRetries?: number;
}

/**
 * LLM Service for evaluating incident tickets
 */
export class LLMService {
  private baseURL: string;
  private apiKey: string;
  private timeout: number;
  private maxRetries: number;

  constructor(config: LLMConfig) {
    this.baseURL = config.apiUrl;
    this.apiKey = config.apiKey;
    this.timeout = config.timeout || 30000;
    this.maxRetries = config.maxRetries || 3;
  }

  /**
   * Evaluate a single incident ticket using LLM
   */
  async evaluateTicket(ticket: IncidentTicket): Promise<LLMEvaluationResponse> {
    const prompt = this.buildEvaluationPrompt(ticket);
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        // Use the configured model or default
        const model = process.env.LLM_MODEL || 'gpt-3.5-turbo';

        const response = await fetch(this.baseURL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            model: model,
            messages: [{
              role: 'user',
              content: prompt
            }],
            temperature: 0.1,
            max_tokens: 500,
            response_format: { type: 'json_object' }
          }),
          signal: AbortSignal.timeout(this.timeout)
        });

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unknown error');
          throw new Error(`HTTP ${response.status}: ${response.statusText}. Response: ${errorText}`);
        }

        const data = await response.json();
        const content = data.choices[0]?.message?.content;

        if (!content) {
          throw new Error('No content in LLM response');
        }

        // Parse LLM response with robust error handling
        const evaluation = this.parseEvaluationResponse(content);
        return {
          ranking: evaluation.ranking || 'Average',
          reviewComments: evaluation.reviewComments || 'No specific feedback provided.'
        };

      } catch (error) {
        console.error(`LLM evaluation attempt ${attempt} failed:`, error);

        if (attempt === this.maxRetries) {
          // Throw error on final failure to ensure data integrity
          throw new Error(`LLM evaluation failed after ${this.maxRetries} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    // This should never be reached due to error throwing above
    throw new Error('LLM evaluation failed: Unexpected code path reached');
  }

  /**
   * Parse LLM evaluation response with robust error handling
   * Handles backticks, markdown formatting, and other potential JSON issues
   */
  private parseEvaluationResponse(content: string): LLMEvaluationResponse {
    try {
      // Log the raw content for debugging
      console.log('[LLM RESPONSE] Raw content:', content.substring(0, 200) + (content.length > 200 ? '...' : ''));

      // Clean the content to handle common formatting issues
      let cleanedContent = content.trim();

      // Remove markdown code blocks if present
      cleanedContent = cleanedContent.replace(/^```(?:json)?\s*\n?/gm, '');
      cleanedContent = cleanedContent.replace(/\n?```\s*$/gm, '');

      // Remove any leading/trailing backticks
      cleanedContent = cleanedContent.replace(/^`+|`+$/g, '');

      // Try to extract JSON from the response if it's wrapped in text
      const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedContent = jsonMatch[0];
      }

      // Parse the cleaned JSON
      const parsed = JSON.parse(cleanedContent);

      // Validate the response structure
      if (typeof parsed !== 'object' || parsed === null) {
        throw new Error('Response is not a valid object');
      }

      // Return with fallback values
      return {
        ranking: this.validateRanking(parsed.ranking) || 'Average',
        reviewComments: typeof parsed.reviewComments === 'string'
          ? parsed.reviewComments
          : 'No specific feedback provided.'
      };

    } catch (error) {
      console.error('[LLM PARSE ERROR] Failed to parse response:', error);
      console.error('[LLM PARSE ERROR] Original content:', content);

      // Try to extract meaningful information from the raw content as fallback
      const fallbackResponse = this.extractFallbackResponse(content);
      if (fallbackResponse) {
        return fallbackResponse;
      }

      // Return error response as last resort
      return {
        ranking: 'Error',
        reviewComments: `Failed to parse LLM response. Raw content: ${content.substring(0, 100)}...`
      };
    }
  }

  /**
   * Validate ranking value
   */
  private validateRanking(ranking: any): LLMEvaluationResponse['ranking'] | null {
    const validRankings: LLMEvaluationResponse['ranking'][] = [
      'Poor', 'Below Average', 'Average', 'Good', 'Excellent', 'Error'
    ];

    if (typeof ranking === 'string' && validRankings.includes(ranking as any)) {
      return ranking as LLMEvaluationResponse['ranking'];
    }

    return null;
  }

  /**
   * Extract fallback response from raw content when JSON parsing fails
   */
  private extractFallbackResponse(content: string): LLMEvaluationResponse | null {
    try {
      // Look for ranking patterns in the text
      const rankingPatterns = [
        /ranking["\s]*:[\s]*["']?(Poor|Below Average|Average|Good|Excellent)["']?/i,
        /quality["\s]*:[\s]*["']?(Poor|Below Average|Average|Good|Excellent)["']?/i,
        /(Poor|Below Average|Average|Good|Excellent)/i
      ];

      let extractedRanking: LLMEvaluationResponse['ranking'] = 'Average';

      for (const pattern of rankingPatterns) {
        const match = content.match(pattern);
        if (match && match[1]) {
          const ranking = this.validateRanking(match[1]);
          if (ranking) {
            extractedRanking = ranking;
            break;
          }
        }
      }

      // Look for comments patterns
      const commentPatterns = [
        /(?:reviewComments|comments|feedback)["\s]*:[\s]*["']([^"']+)["']/i,
        /(?:review|feedback|comments)[\s]*:[\s]*([^\n]+)/i
      ];

      let extractedComments = 'Unable to extract detailed feedback from response.';

      for (const pattern of commentPatterns) {
        const match = content.match(pattern);
        if (match && match[1]) {
          extractedComments = match[1].trim();
          break;
        }
      }

      return {
        ranking: extractedRanking,
        reviewComments: extractedComments
      };

    } catch (error) {
      console.error('[FALLBACK PARSE ERROR]', error);
      return null;
    }
  }

  /**
   * Build evaluation prompt for the LLM
   */
  private buildEvaluationPrompt(ticket: IncidentTicket): string {
    return `
You are an expert incident ticket communication evaluator. Your role is to assess how effectively this incident ticket serves as a **communication for business stakeholders and senior management** who need to understand the current situation.

Incident Details:
- Service: ${ticket.Service}
- Problem Service: ${ticket.ProblemService}
- Summary: ${ticket.Summary}
- Business Impact: ${ticket.BusinessImpact}
- Technical Details: ${ticket.TechnicalDetails}
- Instructions: ${ticket.Instructions}

**Evaluate this ticket's communication effectiveness by asking:**

**Clarity for Non-Technical Audiences:**
- Can a business manager who doesn't understand technical systems quickly grasp what is broken?
- Would a senior manager be able to explain this incident to other stakeholders based on this information?
- Is the information presented in plain English and free of technical jargon?

**Communication Effectiveness:**
- Would this ticket allow a production support manager to provide meaningful updates to business stakeholders?
- Can non-technical staff understand what customers are experiencing and how it affects business operations?
- Is the business impact communicated in terms that business stakeholders can use for decision-making?

**Stakeholder Communication Quality:**
- Could this information be used in executive briefings or customer communications?
- Are timelines and next steps clear enough for business stakeholders?

Please respond with a JSON object containing:
{
  "ranking": "one of: Poor, Below Average, Average, Good, Excellent",
  "reviewComments": "specific, actionable feedback on transforming technical incident details into clear business communication that improves the ticket's effectiveness as a stakeholder communication tool"
}
    `.trim();
  }

}

/**
 * Mock LLM Service for development/testing
 * Focuses on communication effectiveness for business stakeholders
 */
export class MockLLMService {
  async evaluateTicket(ticket: IncidentTicket): Promise<LLMEvaluationResponse> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // Evaluate communication effectiveness for business stakeholders
    const communicationIssues = this.identifyCommunicationIssues(ticket);
    const ranking = this.determineRanking(communicationIssues);
    const reviewComments = this.generateCommunicationFeedback(ticket, communicationIssues);

    return {
      ranking,
      reviewComments
    };
  }

  /**
   * Identify communication issues that would hinder business stakeholder understanding
   */
  private identifyCommunicationIssues(ticket: IncidentTicket): string[] {
    const issues: string[] = [];

    // Check business impact clarity
    if (!ticket.BusinessImpact || ticket.BusinessImpact.length < 20) {
      issues.push('insufficient_business_impact');
    } else if (this.containsExcessiveJargon(ticket.BusinessImpact)) {
      issues.push('technical_jargon_in_business_impact');
    }

    // Check summary accessibility
    if (!ticket.Summary || ticket.Summary.length < 15) {
      issues.push('inadequate_summary');
    } else if (!this.isExecutiveFriendly(ticket.Summary)) {
      issues.push('summary_not_executive_friendly');
    }

    // Check for missing stakeholder context
    if (!this.hasStakeholderContext(ticket)) {
      issues.push('missing_stakeholder_context');
    }

    // Check for unclear customer impact
    if (!this.hasCustomerImpactClarity(ticket)) {
      issues.push('unclear_customer_impact');
    }

    return issues;
  }

  /**
   * Determine ranking based on communication effectiveness
   */
  private determineRanking(issues: string[]): 'Poor' | 'Below Average' | 'Average' | 'Good' | 'Excellent' {
    if (issues.length === 0) return 'Excellent';
    if (issues.length === 1) return 'Good';
    if (issues.length === 2) return 'Average';
    if (issues.length === 3) return 'Below Average';
    return 'Poor';
  }

  /**
   * Generate communication-focused feedback
   */
  private generateCommunicationFeedback(ticket: IncidentTicket, issues: string[]): string {
    if (issues.length === 0) {
      return 'This ticket serves as an excellent communication tool for business stakeholders. The business impact is clear, technical information is accessible, and executives would be able to use this information for decision-making and stakeholder updates.';
    }

    let feedback = 'To improve this ticket as a stakeholder communication tool: ';
    const suggestions: string[] = [];

    if (issues.includes('insufficient_business_impact')) {
      suggestions.push('Expand the business impact section to clearly explain what customers and business operations are experiencing');
    }

    if (issues.includes('technical_jargon_in_business_impact')) {
      suggestions.push('Replace technical terms in the business impact with plain English that executives can understand and communicate to others');
    }

    if (issues.includes('inadequate_summary')) {
      suggestions.push('Provide a more comprehensive summary that a business manager could use to brief senior leadership');
    }

    if (issues.includes('summary_not_executive_friendly')) {
      suggestions.push('Rewrite the summary to be accessible to senior executives who may not have technical background');
    }

    if (issues.includes('missing_stakeholder_context')) {
      suggestions.push('Add context about how this affects different business stakeholders (customers, sales, operations, etc.)');
    }

    if (issues.includes('unclear_customer_impact')) {
      suggestions.push('Clearly describe what customers are experiencing in terms that can be communicated to business leadership');
    }

    return feedback + suggestions.join('. ') + '.';
  }

  /**
   * Check if text contains excessive technical jargon
   */
  private containsExcessiveJargon(text: string): boolean {
    const jargon = ['api', 'ssl', 'tcp', 'dns', 'cpu', 'sql', 'json', 'oauth', 'saml', 'vpn'];
    const jargonCount = jargon.filter(term => text.toLowerCase().includes(term)).length;
    return jargonCount > 2;
  }

  /**
   * Check if summary is executive-friendly
   */
  private isExecutiveFriendly(summary: string): boolean {
    const summaryLower = summary.toLowerCase();
    const hasBusinessTerms = summaryLower.includes('customer') || summaryLower.includes('business') || summaryLower.includes('impact');
    const appropriateLength = summary.length > 30 && summary.length < 200;
    return hasBusinessTerms && appropriateLength;
  }

  /**
   * Check if ticket has stakeholder context
   */
  private hasStakeholderContext(ticket: IncidentTicket): boolean {
    const allText = `${ticket.Summary} ${ticket.BusinessImpact} ${ticket.Instructions}`.toLowerCase();
    return allText.includes('customer') || allText.includes('user') || allText.includes('business');
  }

  /**
   * Check if customer impact is clearly described
   */
  private hasCustomerImpactClarity(ticket: IncidentTicket): boolean {
    const businessImpact = ticket.BusinessImpact?.toLowerCase() || '';
    return businessImpact.includes('customer') && (businessImpact.includes('unable') || businessImpact.includes('cannot') || businessImpact.includes('experiencing'));
  }
}

/**
 * Create LLM service instance with environment configuration
 */
export function createLLMService(): LLMService | MockLLMService {
  const apiUrl = process.env.LLM_API_URL;
  const apiKey = process.env.LLM_API_KEY;
  const useMock = process.env.USE_MOCK_LLM === 'true';

  // Use mock service if explicitly requested or if credentials are missing
  if (useMock || !apiUrl || !apiKey) {
    console.log('[LLM SERVICE] Using mock LLM service for development');
    return new MockLLMService();
  }

  console.log(`[LLM SERVICE] Using real LLM service at ${apiUrl}`);
  return new LLMService({
    apiUrl,
    apiKey,
    timeout: parseInt(process.env.LLM_TIMEOUT || '30000'),
    maxRetries: parseInt(process.env.LLM_MAX_RETRIES || '3')
  });
}
