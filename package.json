{"name": "flash-qa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@types/lodash": "^4.17.20", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "^0.542.0", "next": "14.2.5", "papaparse": "^5.4.1", "react": "^18", "react-dom": "^18", "recharts": "^3.1.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/papaparse": "^5.3.14", "@types/react": "^18", "@types/react-dom": "^18", "babel-jest": "^30.1.2", "eslint": "^8", "eslint-config-next": "14.2.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}