# Data Models

## Core Data Structures

### IncidentTicket Interface

```typescript
interface IncidentTicket {
  incidentNumber: string;           // Unique identifier (e.g., "INC-2024-001234")
  incidentOpenDate: string;         // ISO date string
  impactedServices: string;         // Comma-separated services
  team: string;                     // Responsible team name
  summary: string;                  // Brief incident description
  businessImpact: string;           // Business impact assessment
  latestUpdateAndUserInstructions: string; // Current status and user guidance
  technicalDetails: string;         // Technical information
}
```

### EvaluatedTicket Interface

```typescript
interface EvaluatedTicket extends IncidentTicket {
  ranking: number;                  // LLM-generated score (1-10)
  reviewComments: string;           // LLM-generated feedback
  evaluationStatus: 'pending' | 'processing' | 'completed' | 'failed';
  errorMessage?: string;            // Error details if evaluation failed
  processingTime?: number;          // Time taken for evaluation (ms)
}
```

### ProcessingSession Interface

```typescript
interface ProcessingSession {
  sessionId: string;                // Unique session identifier
  fileName: string;                 // Original uploaded file name
  totalTickets: number;             // Total number of tickets to process
  processedTickets: number;         // Number of completed evaluations
  failedTickets: number;            // Number of failed evaluations
  startTime: Date;                  // Processing start timestamp
  endTime?: Date;                   // Processing completion timestamp
  status: 'initializing' | 'processing' | 'completed' | 'failed';
  tickets: EvaluatedTicket[];       // Array of all tickets in session
}
```

## API Request/Response Models

### File Upload Request

```typescript
interface UploadRequest {
  file: File;                       // CSV file from form data
}

interface UploadResponse {
  sessionId: string;                // Generated session identifier
  fileName: string;                 // Uploaded file name
  totalTickets: number;             // Number of tickets found in CSV
  validationErrors: string[];       // Any validation issues found
  success: boolean;                 // Upload success status
}
```

### Processing Request

```typescript
interface ProcessRequest {
  sessionId: string;                // Session to process
  batchSize?: number;               // Optional batch size (default: 5)
}

interface ProcessResponse {
  sessionId: string;                // Session identifier
  status: 'started' | 'already_processing' | 'error';
  message: string;                  // Status message
}
```

### Status Check Response

```typescript
interface StatusResponse {
  sessionId: string;                // Session identifier
  status: 'processing' | 'completed' | 'failed';
  progress: {
    total: number;                  // Total tickets to process
    processed: number;              // Completed evaluations
    failed: number;                 // Failed evaluations
    percentage: number;             // Completion percentage
  };
  estimatedTimeRemaining?: number;  // Estimated completion time (ms)
  lastUpdated: Date;                // Last status update timestamp
}
```

### Export Request

```typescript
interface ExportRequest {
  sessionId: string;                // Session to export
  includeFailedTickets?: boolean;   // Include failed evaluations
  format?: 'csv' | 'json';          // Export format (default: csv)
}

interface ExportResponse {
  fileName: string;                 // Generated file name
  contentType: string;              // MIME type
  data: string | Buffer;            // File content
  size: number;                     // File size in bytes
}
```

## LLM Integration Models

### LLM Request

```typescript
interface LLMEvaluationRequest {
  ticket: IncidentTicket;           // Ticket to evaluate
  prompt: string;                   // Evaluation prompt template
  maxTokens?: number;               // Maximum response tokens
  temperature?: number;             // Response creativity (0-1)
}

interface LLMEvaluationResponse {
  ranking: number;                  // Quality score (1-10)
  reviewComments: string;           // Detailed feedback
  confidence: number;               // Evaluation confidence (0-1)
  processingTime: number;           // LLM response time (ms)
}
```

### LLM Error Response

```typescript
interface LLMErrorResponse {
  error: {
    code: string;                   // Error code (e.g., 'rate_limit', 'timeout')
    message: string;                // Human-readable error message
    retryAfter?: number;            // Retry delay in seconds
    details?: any;                  // Additional error context
  };
}
```

## UI State Models

### Application State

```typescript
interface AppState {
  currentSession?: ProcessingSession; // Active processing session
  uploadStatus: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;                   // Current error message
  notifications: Notification[];    // User notifications queue
}

interface Notification {
  id: string;                       // Unique notification ID
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;                  // Notification content
  timestamp: Date;                  // Creation time
  autoClose?: boolean;              // Auto-dismiss notification
}
```

### Table State

```typescript
interface TableState {
  data: EvaluatedTicket[];          // Current table data
  filteredData: EvaluatedTicket[];  // Filtered/searched data
  pagination: {
    currentPage: number;            // Current page (1-based)
    pageSize: number;               // Items per page
    totalPages: number;             // Total number of pages
    totalItems: number;             // Total number of items
  };
  sorting: {
    column: keyof EvaluatedTicket;  // Sort column
    direction: 'asc' | 'desc';      // Sort direction
  };
  filters: {
    searchTerm: string;             // Global search term
    teamFilter: string[];           // Selected teams
    scoreRange: [number, number];   // Score range filter
  };
}
```

## Validation Schemas

### CSV Validation Schema

```typescript
const IncidentTicketSchema = z.object({
  'id': z.string().min(1, 'Incident number is required'),
  'ImpactDate': z.string().min(1, 'Open date is required'),
  'Service': z.string().min(1, 'Impacted service required'),
  'ProblemService': z.string().min(1, 'Problem service is required'),
  'Summary': z.string().min(1, 'Summary is required'),
  'BusinessImpact': z.string().min(1, 'Business impact is required'),
  'Instructions': z.string().min(1, 'Instructions are required'),
  'TechnicalDetails': z.string().min(1, 'Technical details are required')
});
```

### Configuration Schema

```typescript
const ConfigSchema = z.object({
  LLM_API_URL: z.string().url('Invalid LLM API URL'),
  LLM_API_KEY: z.string().min(1, 'LLM API key is required'),
  MAX_FILE_SIZE: z.number().positive('Max file size must be positive'),
  MAX_CONCURRENT_REQUESTS: z.number().min(1).max(20, 'Concurrent requests must be 1-20'),
  REQUEST_TIMEOUT: z.number().positive('Request timeout must be positive')
});
```