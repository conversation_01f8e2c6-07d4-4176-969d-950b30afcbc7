'use client';

import React, { useState, useMemo } from 'react';
import { EvaluatedTicket } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3, Filter } from 'lucide-react';
import { BarChart, Bar, XAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell, LabelList } from 'recharts';

interface SummaryStatisticsProps {
  results: EvaluatedTicket[];
}

/**
 * Summary statistics component showing evaluation results overview
 * Displays total processed, successful, failed counts and ranking distribution
 */
export const SummaryStatistics: React.FC<SummaryStatisticsProps> = ({ results }) => {
  const [selectedServices, setSelectedServices] = useState<string[]>([]);



  // Get unique services for filtering
  const services = useMemo(() => {
    const uniqueServices = Array.from(new Set(results.map(r => r.Service)));
    return uniqueServices.sort();
  }, [results]);

  // Filter results by selected services
  const filteredResults = useMemo(() => {
    if (selectedServices.length === 0) return results;
    return results.filter(r => selectedServices.includes(r.Service));
  }, [results, selectedServices]);

  // Calculate ranking distribution
  const rankingDistribution = useMemo(() => {
    const distribution = {
      'Excellent': 0,
      'Good': 0,
      'Average': 0,
      'Below Average': 0,
      'Poor': 0,
      'Error': 0
    };

    filteredResults.forEach(result => {
      if (result.processingStatus === 'complete' && result.ranking) {
        distribution[result.ranking]++;
      }
    });

    return distribution;
  }, [filteredResults]);

  const toggleService = (service: string) => {
    setSelectedServices(prev => 
      prev.includes(service) 
        ? prev.filter(s => s !== service)
        : [...prev, service]
    );
  };

  const clearFilters = () => {
    setSelectedServices([]);
  };

  // Professional color palette for quality rankings
  const getRankingColor = (ranking: string) => {
    const colors = {
      'Excellent': '#059669', // Emerald-600
      'Good': '#16a34a',      // Green-600
      'Average': '#ca8a04',   // Yellow-600
      'Below Average': '#ea580c', // Orange-600
      'Poor': '#dc2626',      // Red-600
      'Error': '#6b7280'      // Gray-500
    };
    return colors[ranking as keyof typeof colors] || '#6b7280';
  };

  // Prepare data for column chart
  const chartData = useMemo(() => {
    return Object.entries(rankingDistribution).map(([ranking, count]) => ({
      ranking,
      count,
      percentage: filteredResults.length > 0 ? (count / filteredResults.length) * 100 : 0,
      fill: getRankingColor(ranking)
    }));
  }, [rankingDistribution, filteredResults.length]);

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">

      {/* Ranking Distribution */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Quality Ranking Distribution
              </CardTitle>
              <CardDescription>
                Distribution of evaluation rankings
                {selectedServices.length > 0 && ` (filtered by ${selectedServices.length} service${selectedServices.length > 1 ? 's' : ''})`}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                disabled={selectedServices.length === 0}
              >
                <Filter className="h-4 w-4 mr-1" />
                Clear Filters
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Service Filter */}
          <div>
            <p className="text-sm font-medium mb-2">Filter by Service:</p>
            <div className="flex flex-wrap gap-2">
              {services.map(service => (
                <Badge
                  key={service}
                  variant={selectedServices.includes(service) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => toggleService(service)}
                >
                  {service}
                </Badge>
              ))}
            </div>
          </div>

          {/* Column Chart */}
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 30, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis
                  dataKey="ranking"
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <Tooltip
                  formatter={(value, name) => [value, name === 'count' ? 'Count' : name]}
                  labelFormatter={(label) => `Ranking: ${label}`}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                />
                <Bar
                  dataKey="count"
                  radius={[4, 4, 0, 0]}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                  <LabelList
                    dataKey="count"
                    position="top"
                    style={{
                      fontSize: '14px',
                      fontWeight: '600',
                      fill: 'hsl(var(--foreground))'
                    }}
                  />
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
