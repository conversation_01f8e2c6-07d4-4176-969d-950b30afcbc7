### **Incident Ticket Quality Evaluator Product Requirements Document (PRD)**

**Version:** 1.0
**Date:** August 31, 2025

### **1. Goals and Background Context**

#### **Goals**
* To improve the clarity, completeness, and consistency of incident ticket documentation.
* To reduce the time required for responders and managers to understand the full context and impact of an incident.
* To establish a data-driven baseline for incident documentation quality.

#### **Background Context**
The current process for creating production incident tickets often results in inconsistent quality and missing information, which delays triage and resolution. This project will address the problem by creating a Next.js web application that uses an in-house LLM to automatically evaluate the quality of incident tickets submitted via a CSV file. The tool will provide immediate, actionable feedback to help train users and provide management with insights into documentation quality.

#### **Change Log**
| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| August 31, 2025 | 1.0 | Initial PRD draft | John (PM) |

---
### **2. Requirements**

#### **Functional Requirements**
1.  **FR1**: The system shall provide an interface for a user to upload a CSV file.
2.  **FR2**: The system shall parse a CSV file and specifically process the following columns: `id`, `ImpactDate`, `Service`, `ProblemService`, `Summary`, `BusinessImpact`, `Instructions`, and `TechnicalDetails`.
3.  **FR3**: For each row in the uploaded CSV, the system shall make an API call to the in-house, OpenAI-compatible LLM to evaluate the ticket's quality.
4.  **FR4**: The system shall display the results in a paginated on-screen table, including all original data plus the LLM-generated `Ranking` and `Review Comments` columns.
5.  **FR5**: The on-screen results table shall include a function to search by `incident id` and `team`, and a function to filter by `score`.
6.  **FR6**: The system shall generate and display summary statistics (Total processed, successful, failed) for the current upload.
7.  **FR7**: The system shall generate and display a chart showing the distribution of scores, which can be filtered by `team` (with multi-select).
8.  **FR8**: The system shall provide a function for the user to download the complete results (including new `Ranking` and `Review Comments` columns) as a new CSV file.

#### **Non-Functional Requirements**
1.  **NFR1**: The application must be built using Next.js for both its frontend and backend components.
2.  **NFR2**: The MVP architecture must be stateless; no data from user uploads will be stored in a persistent database.
3.  **NFR3**: The implementation must prioritize simplicity and focus on core functionality, avoiding premature optimization or complex abstractions.
4.  **NFR4**: The user interface must provide clear, real-time feedback during processing, including progress status and counts of pending, in-progress, and completed records.
5.  **NFR5**: The prompt engineering for the LLM must ensure that feedback on the `business impact`, `latest update`, and `user instruction` fields is in plain English for a non-technical audience, while the `technical details` field remains precise for an IT audience.
6.  **NFR6**: The system's CSV parsing logic must be flexible, gracefully ignoring any additional columns not on the required list without causing an error.

---
### **3. User Interface Design Goals**

#### **Overall UX Vision**
The user experience should be simple, clean, and efficient. The primary goal is to provide a frictionless process for a user to upload their file, receive a quality evaluation, and understand the results. The interface should be intuitive, requiring no training to use.

#### **Key Interaction Paradigms**
The application will function as a single-page application (SPA). The user journey will be contained within a single view that transitions through three main states without a full page reload:
1.  **Upload State**: The initial view where the user can drag-and-drop or select their CSV file.
2.  **Processing State**: After upload, the view will update to show the "Live Processing View," displaying progress bars, status counts, and the results table as it populates in real-time.
3.  **Results State**: The final view showing the completed, searchable, and filterable results table, along with the MI charts and the download button.

#### **Core Screens and Views**
Conceptually, the application consists of one primary screen that dynamically updates to show the following views:
* File Upload View
* Live Processing & Results View

#### **Accessibility**
* **Assumption**: The application should meet **WCAG 2.1 Level AA** standards to ensure it is usable by people with disabilities.

#### **Branding**
* **To Be Determined**: No branding guidelines have been provided at this time.

#### **Target Device and Platforms**
* **Assumption**: The application will be a **Web Responsive** application, ensuring a functional experience on modern desktop, tablet, and mobile web browsers.

---
### **4. Technical Assumptions**

#### **Repository Structure**
* **Monorepo**: The entire application (frontend and backend Next.js routes) will be contained within a single Git repository to simplify development and deployment for the MVP.

#### **Service Architecture**
* **Monolith with Serverless Functions**: The application will use the standard Next.js architecture, which functions as a monolith where API routes are deployed as individual serverless functions. This approach is simple, scalable, and cost-effective.

#### **Testing Requirements**
* **Unit + Integration**: The testing strategy will focus on unit tests for individual components and functions, supplemented by integration tests for key user flows. Full end-to-end (E2E) testing will be deferred post-MVP.

#### **Additional Technical Assumptions and Requests**
* **Stateless MVP**: The application will be stateless, with no persistent database in the initial version.
* **LLM Integration**: The application will integrate with an in-house hosted, OpenAI-compatible LLM API.

---
### **5. Epic List**

* **Epic 1: Core Processing Pipeline & CSV Generation**
    * **Goal**: Establish the core application and enable a user to upload a CSV file, have it fully processed by the LLM, and receive the complete, enriched results as a downloadable CSV file.

* **Epic 2: Interactive Results Dashboard**
    * **Goal**: Enhance the user experience by displaying the processed results in a rich, interactive, on-screen dashboard with search, filtering, and data visualization capabilities.

---
### **6. Epic 1: Core Processing Pipeline & CSV Generation**

**Expanded Goal**: This epic establishes the foundational Next.js application and delivers the complete, end-to-end data processing workflow. Upon completion, a user will be able to upload a CSV of incident tickets, have them evaluated by the in-house LLM, and download a new CSV containing the quality analysis, proving the core value of the product.

#### **Story 1.1: Initial Project Setup and UI Shell**
* **As a** developer, **I want** to initialize a new Next.js project and create a basic UI shell, **so that** we have a foundational structure to build all subsequent features upon.
* **Acceptance Criteria:**
    1. A new Next.js application is created using the latest stable version.
    2. The application runs successfully in a local development environment.
    3. A single main page is created with a basic layout (e.g., header, main content area, footer).
    4. The main content area contains a placeholder for the future file upload component.

#### **Story 1.2: Implement CSV File Upload Component**
* **As a** user, **I want** to select a CSV file from my computer and upload it to the application, **so that** I can submit my incident data for evaluation.
* **Acceptance Criteria:**
    1. The UI shell displays a file input or drag-and-drop area.
    2. The component only accepts files with a `.csv` extension.
    3. A user is shown an error message if they attempt to upload a non-CSV file.
    4. Upon successful selection, the file is held in the client-side state, ready to be sent to the backend.

#### **Story 1.3: Create API Endpoint for CSV Processing**
* **As a** developer, **I want** to create a backend API endpoint that accepts a CSV file, **so that** the frontend has a target to send the user's data for processing.
* **Acceptance Criteria:**
    1. A Next.js API route is created at `/api/evaluate`.
    2. The endpoint accepts a `multipart/form-data` request containing a file.
    3. The endpoint successfully parses the uploaded CSV file into a data structure.
    4. The endpoint returns a `200 OK` success response with the total number of rows detected in the CSV.

#### **Story 1.4: Integrate LLM Service for a Single Row**
* **As a** developer, **I want** to create a service that can send the data from a single CSV row to the in-house LLM and receive a response, **so that** we can validate the core LLM integration.
* **Acceptance Criteria:**
    1. A service module is created that can successfully connect to the in-house, OpenAI-compatible LLM API.
    2. The service can take the data for a single incident and format it into the correct prompt.
    3. The service successfully receives a `Ranking` and `Review Comments` from the LLM.
    4. The service gracefully handles potential errors from the LLM API.

#### **Story 1.5: Orchestrate Full CSV Processing Loop**
* **As a** developer, **I want** the API endpoint to loop through all rows of the parsed CSV and call the LLM service for each one, **so that** an entire batch of incidents can be evaluated.
* **Acceptance Criteria:**
    1. The `/api/evaluate` endpoint iterates through every row of the parsed CSV data.
    2. For each row, it calls the LLM integration service created in Story 1.4.
    3. It aggregates all results (both successful and failed evaluations) into a single collection.
    4. The process continues even if some individual rows fail the LLM evaluation.

#### **Story 1.6: Generate and Download Results CSV**
* **As a** user, **I want** to receive a downloadable CSV file containing the results of the evaluation, **so that** I can have a portable record of the feedback.
* **Acceptance Criteria:**
    1. The `/api/evaluate` endpoint takes the aggregated results from the processing loop.
    2. It generates a new CSV file in the correct format.
    3. This new CSV contains all the original data from the uploaded file, plus the new `Ranking` and `Review Comments` columns.
    4. The API endpoint's response triggers a file download in the user's browser.

---
### **7. Epic 2: Interactive Results Dashboard**

**Expanded Goal**: This epic builds upon the core processing pipeline from Epic 1 by creating a rich, client-side user interface for the results. Upon completion, a user will no longer need to download the CSV to see their results; they will be able to view, search, filter, and analyze the data directly within the application, providing a more immediate and interactive experience.

#### **Story 2.1: Display Real-Time Processing Progress**
* **As a** user, **I want** to see live feedback and progress after I upload my file, **so that** I know the system is working and have an idea of the evaluation's status.
* **Acceptance Criteria:**
    1. After a successful file upload, the UI transitions to a "Processing" state.
    2. This state displays an overall progress bar for the entire file.
    3. The UI shows live-updating counts for records that are "Pending," "In Progress," "Successful," and "Failed."
    4. The underlying mechanism to fetch these updates is implemented.

#### **Story 2.2: Implement Paginated Results Table**
* **As a** user, **I want** to see my evaluation results in a clear, paginated table on the screen, **so that** I can easily review them without needing to open a separate spreadsheet application.
* **Acceptance Criteria:**
    1. As records are successfully processed, they appear in a table component on the page.
    2. The table correctly displays all original data columns plus the new `Ranking` and `Review Comments` columns.
    3. The table is paginated, showing a fixed number of rows per page.
    4. Pagination controls are present and functional.

#### **Story 2.3: Add Search and Filter Functionality to Table**
* **As a** user, **I want** to search and filter my results, **so that** I can quickly find specific incidents or analyze subsets of my data.
* **Acceptance Criteria:**
    1. A search input field is added to the UI to filter by `incident id` and `team`.
    2. Dropdown menus are added to filter the results by `score`.
    3. Multiple filters can be applied simultaneously.
    4. The table's pagination updates correctly to reflect the filtered data set.

#### **Story 2.4: Implement Summary Statistics Display**
* **As a** user, **I want** to see a high-level summary of the evaluation, **so that** I can understand the overall quality of the batch at a glance.
* **Acceptance Criteria:**
    1. Once processing is complete, a summary component is displayed on the UI.
    2. The component accurately shows the total number of records processed, the count of successful records, and the count of failed records.

#### **Story 2.5: Implement Score Distribution Chart**
* **As a** user, **I want** to see a chart of the score distribution, **so that** I can visually assess the quality spread of the incident tickets.
* **Acceptance Criteria:**
    1. A bar chart is displayed in the results view.
    2. The chart correctly displays the number of incidents for each score category.
    3. The chart dynamically updates when the `team` filter is applied.

